# 多模态对话情感分析系统

## 📋 项目概述

这是一个基于emotion2vec的多模态对话情感分析系统，能够同时分析音频和文本的情感信息，并通过多模态融合技术提供更准确的情感识别结果。

## 🏗️ 系统架构

```
多模态对话分析系统
├── audio_processing.py      # 音频处理模块（ASR + 音频情感分析）
├── text_processing.py       # 文本处理模块（文本情感分析 + 特征提取）
├── multimodal_fusion.py     # 多模态融合模块
├── conversation_analyzer.py # 主分析系统
└── requirements.txt         # 依赖文件
```

## 🚀 功能特性

### 🎵 音频处理
- **语音转录**: 使用FunASR进行中文语音识别
- **音频情感分析**: 基于emotion2vec的音频情感识别
- **音频预处理**: 自动转换采样率和声道数

### 📝 文本处理
- **文本情感分析**: 基于规则和词典的中文情感分析
- **文本预处理**: 分词、去噪、标准化
- **特征提取**: 支持BERT特征提取（可选）

### 👥 说话人分离
- **多说话人识别**: 自动检测音频中的不同说话人
- **时间戳标注**: 精确标记每个说话人的发言时间
- **片段分离**: 将音频按说话人分割成独立片段
- **智能回退**: 模型不可用时使用基于能量的简单分离

### 🔄 多模态融合
- **自适应融合**: 根据置信度动态调整融合策略
- **一致性增强**: 当音频和文本情感一致时增强置信度
- **智能选择**: 置信度差距大时优先选择高置信度结果
- **保守策略**: 双模态低置信度时采用保守的中立判断

## 📦 安装说明

### 1. 环境要求
- Python 3.8+
- PyTorch 1.13+
- 已配置的emotion2vec环境

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 可选依赖
```bash
# 高级文本处理
pip install transformers

# 说话人分离（未来功能）
pip install pyannote.audio
```

## 🎯 使用方法

### 快速开始

#### 单说话人分析
```python
from conversation_analyzer import ConversationAnalysisSystem

# 初始化系统
system = ConversationAnalysisSystem()

# 分析音频文件
result = system.analyze_single_audio("your_audio.wav")

# 打印分析报告
system.print_analysis_report(result)
```

#### 多说话人分析
```python
from conversation_analyzer import ConversationAnalysisSystem

# 初始化系统
system = ConversationAnalysisSystem()

# 多说话人分析
multi_result = system.analyze_multi_speaker_audio("conversation.wav")

# 打印分析报告
system.print_analysis_report(multi_result)
```

#### 说话人分离独立使用
```python
from speaker_diarization import SpeakerDiarizationProcessor, MultiSpeakerEmotionAnalyzer

# 仅进行说话人分离
diarizer = SpeakerDiarizationProcessor()
diarization_result = diarizer.diarize_audio("conversation.wav")

# 完整的多说话人情感分析
analyzer = MultiSpeakerEmotionAnalyzer()
analysis_result = analyzer.analyze_multi_speaker_audio("conversation.wav")
```

### 命令行使用
```bash
cd multimodal_conversation_analysis

# 运行主系统（支持选择单/多说话人模式）
python conversation_analyzer.py

# 专门测试说话人分离功能
python test_speaker_diarization.py

# 综合测试所有功能
python test_comprehensive.py
```

## 📊 输出结果

### 单说话人分析结果

系统会生成包含以下信息的详细分析报告：

```json
{
  "timestamp": "2024-01-01T12:00:00",
  "audio_file": "example.wav",
  "transcription": {
    "text": "转录的文本内容",
    "confidence": 0.95
  },
  "audio_emotion": {
    "emotion": "开心",
    "confidence": 0.87
  },
  "text_emotion": {
    "emotion": "开心", 
    "confidence": 0.82
  },
  "multimodal_fusion": {
    "fused_emotion": "开心",
    "fused_confidence": 0.89,
    "strategy": "一致性增强"
  }
}
```

### 多说话人分析结果

多说话人分析会提供更详细的分离和情感分析信息：

```json
{
  "timestamp": "2024-01-01T12:00:00",
  "audio_path": "conversation.wav",
  "diarization": {
    "num_speakers": 2,
    "total_duration": 30.5,
    "method": "pyannote",
    "segments": [
      {
        "speaker": "SPEAKER_00",
        "start": 0.0,
        "end": 5.2
      },
      {
        "speaker": "SPEAKER_01", 
        "start": 5.5,
        "end": 12.3
      }
    ]
  },
  "segment_results": [
    {
      "speaker": "SPEAKER_00",
      "start_time": 0.0,
      "end_time": 5.2,
      "transcription": "你好，今天心情怎么样？",
      "audio_emotion": "中立",
      "audio_confidence": 0.85,
      "text_emotion": "中立",
      "text_confidence": 0.78,
      "fused_emotion": "中立",
      "fused_confidence": 0.82
    }
  ],
  "summary": {
    "total_segments": 4,
    "emotion_statistics": {
      "开心": 2,
      "中立": 1,
      "难过": 1
    },
    "average_confidence": 0.84
  }
}
```

## 🔧 配置选项

### 音频处理配置
- 采样率: 16kHz（自动转换）
- 声道: 单声道（自动转换）
- 支持格式: WAV, MP3, FLAC等

### 文本处理配置
- 情感类别: 7类（生气、厌恶、恐惧、开心、中立、难过、吃惊）
- 分词工具: jieba
- 预处理: 自动去噪和标准化

### 融合策略配置
- 默认权重: 音频60%，文本40%
- 一致性阈值: 可调节
- 融合方法: 加权平均（可扩展）

## 📈 性能特点

### 优势
- **多模态互补**: 音频和文本信息相互补充
- **鲁棒性强**: 单模态失效时仍能工作
- **实时处理**: 支持流式音频处理
- **中文优化**: 专门针对中文语音和文本优化

### 适用场景
- 客服质量监控
- 心理健康评估
- 教育场景分析
- 人机交互优化

## 🛠️ 扩展功能

### 已实现的高级功能
- **说话人分离**: 多人对话中的角色识别和时间戳标注
- **自适应融合**: 智能的多模态情感融合策略
- **置信度评估**: 全面的置信度计算和评估机制

### 计划中的功能
- **时序建模**: 情感变化趋势分析
- **实时分析**: 流式音频实时处理
- **多语言支持**: 扩展到其他语言
- **情感强度**: 细粒度的情感强度分析

### 自定义扩展
- 可替换文本情感分析模型
- 可调整融合策略和权重
- 可添加新的情感类别
- 可集成其他音频特征
- 可自定义说话人分离模型

## 🐛 故障排除

### 常见问题
1. **模型加载失败**: 检查网络连接和模型下载
2. **音频格式不支持**: 使用convert_audio.py转换格式
3. **中文分词错误**: 检查jieba安装和词典
4. **内存不足**: 减少批处理大小或使用更小的模型

### 日志调试
系统提供详细的日志信息，可通过调整日志级别获取更多调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📄 许可证

本项目基于原emotion2vec项目的许可证，仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进系统功能。

## 📞 技术支持

如有问题，请查看：
1. 项目文档和示例
2. 常见问题解答
3. GitHub Issues
4. 相关论文和技术博客