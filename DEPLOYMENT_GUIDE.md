# emotion2vec 本地部署指南

## 概述

emotion2vec是一个强大的语音情感识别和特征提取模型。本指南将帮助您在本地环境中快速部署和使用emotion2vec。

## 系统要求

- Python 3.8+
- Windows/Linux/macOS
- 至少4GB内存
- 建议使用GPU（可选）

## 快速开始

### 方法1: 使用自动安装脚本（推荐）

1. **运行安装脚本**
   ```bash
   # Windows用户
   install.bat
   
   # Linux/macOS用户
   pip install -U funasr torch soundfile numpy
   ```

2. **运行部署工具**
   ```bash
   python deploy_emotion2vec.py
   ```

### 方法2: 手动安装

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **测试安装**
   ```python
   from funasr import AutoModel
   print("安装成功!")
   ```

## 使用方式

### 1. 情感识别（emotion2vec+）

emotion2vec+可以识别9种情感：
- 0: angry (愤怒)
- 1: disgusted (厌恶)
- 2: fearful (恐惧)
- 3: happy (快乐)
- 4: neutral (中性)
- 5: other (其他)
- 6: sad (悲伤)
- 7: surprised (惊讶)
- 8: unknown (未知)

```python
from funasr import AutoModel

# 加载模型
model = AutoModel(
    model="iic/emotion2vec_plus_large",
    hub="ms"  # 中国大陆用户使用"ms"，海外用户使用"hf"
)

# 进行情感识别
result = model.generate("your_audio.wav", granularity="utterance", extract_embedding=False)
print(result)
```

### 2. 特征提取（emotion2vec）

```python
from funasr import AutoModel

# 加载特征提取模型
model = AutoModel(
    model="iic/emotion2vec_base",
    hub="ms"
)

# 提取特征
result = model.generate("your_audio.wav", granularity="utterance")
features = result[0]['feats']  # 768维特征向量
```

## 音频要求

- **格式**: WAV文件
- **采样率**: 16kHz
- **声道**: 单声道
- **时长**: 建议1-30秒

## 可用模型

### emotion2vec+系列（情感识别）
- `iic/emotion2vec_plus_seed`: 轻量级模型
- `iic/emotion2vec_plus_base`: 基础模型 (~90M参数)
- `iic/emotion2vec_plus_large`: 大型模型 (~300M参数，推荐)

### emotion2vec系列（特征提取）
- `iic/emotion2vec_base`: 基础特征提取模型

## 高级用法

### 批量处理

```python
# 支持wav.scp格式的批量处理
# 创建wav.scp文件:
# audio1 /path/to/audio1.wav
# audio2 /path/to/audio2.wav

result = model.generate("wav.scp", granularity="utterance")
```

### 帧级特征提取

```python
# 提取帧级特征（每帧50Hz）
result = model.generate("audio.wav", granularity="frame")
frame_features = result[0]['feats']  # [T, 768]
```

## 故障排除

### 常见问题

1. **模型下载失败**
   - 检查网络连接
   - 尝试切换hub参数：`hub="hf"`（海外）或`hub="ms"`（中国）

2. **音频格式错误**
   - 确保音频是16kHz单声道WAV格式
   - 使用ffmpeg转换：`ffmpeg -i input.mp3 -ar 16000 -ac 1 output.wav`

3. **内存不足**
   - 使用较小的模型（seed或base版本）
   - 处理较短的音频片段

4. **CUDA错误**
   - 模型会自动使用GPU（如果可用）
   - 如果遇到CUDA问题，可以强制使用CPU

### 性能优化

- 使用GPU可以显著提升处理速度
- 批量处理多个文件时效率更高
- 对于实时应用，建议使用base或seed模型

## 示例代码

完整的使用示例请参考 `deploy_emotion2vec.py` 文件。

## 技术支持

- GitHub: https://github.com/ddlBoJack/emotion2vec
- 论文: emotion2vec: Self-Supervised Pre-Training for Speech Emotion Representation
- 微信群: 见项目README中的二维码

## 许可证

MIT License