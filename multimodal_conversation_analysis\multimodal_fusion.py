#!/usr/bin/env python3
"""
多模态融合模块
将音频和文本特征进行融合，实现多模态情感分析
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureFusionNetwork(nn.Module):
    """特征融合网络"""
    
    def __init__(self, 
                 audio_dim: int = 768, 
                 text_dim: int = 768, 
                 hidden_dim: int = 256,
                 num_emotions: int = 7,
                 fusion_method: str = "concat"):
        """
        初始化融合网络
        
        Args:
            audio_dim: 音频特征维度
            text_dim: 文本特征维度
            hidden_dim: 隐藏层维度
            num_emotions: 情感类别数
            fusion_method: 融合方法 ("concat", "attention", "gated")
        """
        super().__init__()
        
        self.audio_dim = audio_dim
        self.text_dim = text_dim
        self.hidden_dim = hidden_dim
        self.num_emotions = num_emotions
        self.fusion_method = fusion_method
        
        # 特征投影层
        self.audio_projection = nn.Linear(audio_dim, hidden_dim)
        self.text_projection = nn.Linear(text_dim, hidden_dim)
        
        # 融合层
        if fusion_method == "concat":
            self.fusion_layer = nn.Linear(hidden_dim * 2, hidden_dim)
        elif fusion_method == "attention":
            self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8)
        elif fusion_method == "gated":
            self.gate = nn.Linear(hidden_dim * 2, hidden_dim)
            
        # 分类层
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, num_emotions)
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, audio_features: torch.Tensor, text_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            audio_features: 音频特征 [batch_size, audio_dim]
            text_features: 文本特征 [batch_size, text_dim]
            
        Returns:
            情感预测结果 [batch_size, num_emotions]
        """
        # 特征投影
        audio_proj = F.relu(self.audio_projection(audio_features))
        text_proj = F.relu(self.text_projection(text_features))
        
        # 特征融合
        if self.fusion_method == "concat":
            fused_features = self._concat_fusion(audio_proj, text_proj)
        elif self.fusion_method == "attention":
            fused_features = self._attention_fusion(audio_proj, text_proj)
        elif self.fusion_method == "gated":
            fused_features = self._gated_fusion(audio_proj, text_proj)
        else:
            raise ValueError(f"不支持的融合方法: {self.fusion_method}")
        
        # 分类预测
        output = self.classifier(fused_features)
        return output
    
    def _concat_fusion(self, audio_features: torch.Tensor, text_features: torch.Tensor) -> torch.Tensor:
        """拼接融合"""
        concatenated = torch.cat([audio_features, text_features], dim=-1)
        return F.relu(self.fusion_layer(concatenated))
    
    def _attention_fusion(self, audio_features: torch.Tensor, text_features: torch.Tensor) -> torch.Tensor:
        """注意力融合"""
        # 将特征reshape为序列格式
        audio_seq = audio_features.unsqueeze(1)  # [batch, 1, hidden_dim]
        text_seq = text_features.unsqueeze(1)    # [batch, 1, hidden_dim]
        
        # 交叉注意力
        audio_attended, _ = self.attention(audio_seq, text_seq, text_seq)
        text_attended, _ = self.attention(text_seq, audio_seq, audio_seq)
        
        # 平均池化
        fused = (audio_attended.squeeze(1) + text_attended.squeeze(1)) / 2
        return fused
    
    def _gated_fusion(self, audio_features: torch.Tensor, text_features: torch.Tensor) -> torch.Tensor:
        """门控融合"""
        concatenated = torch.cat([audio_features, text_features], dim=-1)
        gate_weights = torch.sigmoid(self.gate(concatenated))
        
        # 加权融合
        fused = gate_weights * audio_features + (1 - gate_weights) * text_features
        return fused

class MultiModalEmotionAnalyzer:
    """多模态情感分析器"""
    
    def __init__(self, fusion_method: str = "concat"):
        """
        初始化多模态分析器
        
        Args:
            fusion_method: 融合方法
        """
        self.fusion_method = fusion_method
        self.emotion_labels = [
            "生气", "厌恶", "恐惧", "开心", "中立", "难过", "吃惊"
        ]
        
        # 初始化融合网络
        self.fusion_network = FeatureFusionNetwork(
            fusion_method=fusion_method,
            num_emotions=len(self.emotion_labels)
        )
        
        # 设置为评估模式
        self.fusion_network.eval()
        
        logger.info(f"✅ 多模态情感分析器初始化完成 (融合方法: {fusion_method})")
    
    def _extract_audio_features(self, audio_emotion_result: Dict) -> np.ndarray:
        """从音频情感分析结果中提取特征"""
        try:
            # 使用情感得分作为特征
            if 'all_emotions' in audio_emotion_result:
                scores = [score for _, score in audio_emotion_result['all_emotions']]
                # 补齐到768维
                features = np.zeros(768)
                features[:len(scores)] = scores
                return features
            else:
                # 使用简单的one-hot编码
                features = np.zeros(768)
                if audio_emotion_result.get('top_emotion') in self.emotion_labels:
                    idx = self.emotion_labels.index(audio_emotion_result['top_emotion'])
                    features[idx] = audio_emotion_result.get('confidence', 0.5)
                return features
        except Exception as e:
            logger.warning(f"音频特征提取失败: {e}")
            return np.zeros(768)
    
    def _extract_text_features(self, text_emotion_result: Dict) -> np.ndarray:
        """从文本情感分析结果中提取特征"""
        try:
            # 使用情感得分作为特征
            if 'all_scores' in text_emotion_result:
                scores = list(text_emotion_result['all_scores'].values())
                # 补齐到768维
                features = np.zeros(768)
                features[:len(scores)] = scores
                return features
            else:
                # 使用简单的one-hot编码
                features = np.zeros(768)
                if text_emotion_result.get('emotion') in self.emotion_labels:
                    idx = self.emotion_labels.index(text_emotion_result['emotion'])
                    features[idx] = text_emotion_result.get('confidence', 0.5)
                return features
        except Exception as e:
            logger.warning(f"文本特征提取失败: {e}")
            return np.zeros(768)
    
    def analyze(self, audio_emotion_result: Dict, text_emotion_result: Dict) -> Dict:
        """
        多模态情感分析
        
        Args:
            audio_emotion_result: 音频情感分析结果
            text_emotion_result: 文本情感分析结果
            
        Returns:
            融合后的情感分析结果
        """
        try:
            logger.info("开始多模态情感分析...")
            
            # 提取特征
            audio_features = self._extract_audio_features(audio_emotion_result)
            text_features = self._extract_text_features(text_emotion_result)
            
            # 转换为张量
            audio_tensor = torch.FloatTensor(audio_features).unsqueeze(0)
            text_tensor = torch.FloatTensor(text_features).unsqueeze(0)
            
            # 融合预测
            with torch.no_grad():
                fusion_output = self.fusion_network(audio_tensor, text_tensor)
                probabilities = F.softmax(fusion_output, dim=-1)
                
            # 解析结果
            probs = probabilities.squeeze().numpy()
            emotion_scores = list(zip(self.emotion_labels, probs))
            emotion_scores.sort(key=lambda x: x[1], reverse=True)
            
            top_emotion, top_score = emotion_scores[0]
            
            # 计算一致性得分
            audio_emotion = audio_emotion_result.get('top_emotion', '未知')
            text_emotion = text_emotion_result.get('emotion', '未知')
            consistency_score = 1.0 if audio_emotion == text_emotion else 0.5
            
            logger.info(f"多模态分析完成: {top_emotion} ({top_score:.4f})")
            
            return {
                'fused_emotion': top_emotion,
                'fused_confidence': float(top_score),
                'all_emotions': emotion_scores,
                'audio_emotion': audio_emotion,
                'text_emotion': text_emotion,
                'consistency_score': consistency_score,
                'fusion_method': self.fusion_method,
                'audio_confidence': audio_emotion_result.get('confidence', 0.0),
                'text_confidence': text_emotion_result.get('confidence', 0.0)
            }
            
        except Exception as e:
            logger.error(f"多模态分析失败: {e}")
            return {
                'fused_emotion': '错误',
                'fused_confidence': 0.0,
                'error': str(e)
            }

class SimpleMultiModalFusion:
    """简化版多模态融合（基于规则）"""
    
    def __init__(self):
        self.emotion_labels = [
            "生气", "厌恶", "恐惧", "开心", "中立", "难过", "吃惊"
        ]
        # 置信度阈值配置
        self.high_confidence_threshold = 0.8  # 高置信度阈值
        self.low_confidence_threshold = 0.6   # 低置信度阈值
        self.confidence_gap_threshold = 0.3   # 置信度差距阈值
    
    def fuse_emotions(self, audio_result: Dict, text_result: Dict) -> Dict:
        """
        融合音频和文本情感分析结果（主要接口方法）
        
        Args:
            audio_result: 音频情感分析结果
            text_result: 文本情感分析结果
            
        Returns:
            融合结果
        """
        return self.adaptive_fusion(audio_result, text_result)
    
    def adaptive_fusion(self, audio_result: Dict, text_result: Dict) -> Dict:
        """
        自适应融合方法 - 根据置信度动态调整权重
        
        Args:
            audio_result: 音频情感分析结果
            text_result: 文本情感分析结果
            
        Returns:
            融合结果
        """
        try:
            audio_emotion = audio_result.get('top_emotion', '中立')
            text_emotion = text_result.get('emotion', '中立')
            
            audio_conf = audio_result.get('confidence', 0.5)
            text_conf = text_result.get('confidence', 0.5)
            
            logger.info(f"融合输入 - 音频: {audio_emotion}({audio_conf:.4f}), 文本: {text_emotion}({text_conf:.4f})")
            
            # 策略1: 如果两个模态情感一致，增强置信度
            if audio_emotion == text_emotion:
                fused_emotion = audio_emotion
                # 一致时，取较高置信度并增加奖励
                fused_confidence = min(1.0, max(audio_conf, text_conf) + 0.1)
                consistency = 1.0
                fusion_strategy = "一致性增强"
                
            # 策略2: 置信度差距很大时，选择高置信度的结果
            elif abs(audio_conf - text_conf) > self.confidence_gap_threshold:
                if audio_conf > text_conf:
                    fused_emotion = audio_emotion
                    fused_confidence = audio_conf
                    fusion_strategy = "音频主导(高置信度)"
                else:
                    fused_emotion = text_emotion
                    fused_confidence = text_conf
                    fusion_strategy = "文本主导(高置信度)"
                consistency = 0.3
                
            # 策略3: 有一个模态置信度很高(>0.8)，另一个较低
            elif audio_conf > self.high_confidence_threshold and text_conf < self.low_confidence_threshold:
                fused_emotion = audio_emotion
                fused_confidence = audio_conf
                fusion_strategy = "音频主导(高置信度优先)"
                consistency = 0.4
                
            elif text_conf > self.high_confidence_threshold and audio_conf < self.low_confidence_threshold:
                fused_emotion = text_emotion
                fused_confidence = text_conf
                fusion_strategy = "文本主导(高置信度优先)"
                consistency = 0.4
                
            # 策略4: 两个模态置信度都较低，使用保守策略
            elif audio_conf < self.low_confidence_threshold and text_conf < self.low_confidence_threshold:
                # 选择置信度稍高的，但降低最终置信度
                if audio_conf >= text_conf:
                    fused_emotion = audio_emotion
                    fused_confidence = audio_conf * 0.8  # 降低置信度
                else:
                    fused_emotion = text_emotion
                    fused_confidence = text_conf * 0.8
                fusion_strategy = "保守选择(低置信度)"
                consistency = 0.2
                
            # 策略5: 默认加权融合（置信度相近的情况）
            else:
                # 动态计算权重
                total_conf = audio_conf + text_conf
                if total_conf > 0:
                    audio_weight = audio_conf / total_conf
                    text_weight = text_conf / total_conf
                else:
                    audio_weight = 0.6  # 默认音频权重稍高
                    text_weight = 0.4
                
                # 选择加权后置信度更高的情感
                audio_weighted_score = audio_conf * audio_weight
                text_weighted_score = text_conf * text_weight
                
                if audio_weighted_score > text_weighted_score:
                    fused_emotion = audio_emotion
                    fused_confidence = audio_weighted_score
                else:
                    fused_emotion = text_emotion
                    fused_confidence = text_weighted_score
                    
                fusion_strategy = f"动态加权(音频:{audio_weight:.2f}, 文本:{text_weight:.2f})"
                consistency = 0.5
            
            logger.info(f"融合策略: {fusion_strategy}")
            logger.info(f"融合结果: {fused_emotion} ({fused_confidence:.4f})")
            
            return {
                'fused_emotion': fused_emotion,
                'fused_confidence': fused_confidence,
                'audio_emotion': audio_emotion,
                'text_emotion': text_emotion,
                'consistency_score': consistency,
                'fusion_method': 'adaptive',
                'fusion_strategy': fusion_strategy,
                'audio_confidence': audio_conf,
                'text_confidence': text_conf,
                'confidence_gap': abs(audio_conf - text_conf)
            }
            
        except Exception as e:
            logger.error(f"自适应融合失败: {e}")
            return {
                'fused_emotion': '错误',
                'fused_confidence': 0.0,
                'error': str(e)
            }
        
    def weighted_fusion(self, audio_result: Dict, text_result: Dict, 
                       audio_weight: float = 0.6, text_weight: float = 0.4) -> Dict:
        """
        加权融合方法
        
        Args:
            audio_result: 音频情感分析结果
            text_result: 文本情感分析结果
            audio_weight: 音频权重
            text_weight: 文本权重
            
        Returns:
            融合结果
        """
        try:
            audio_emotion = audio_result.get('top_emotion', '中立')
            text_emotion = text_result.get('emotion', '中立')
            
            audio_conf = audio_result.get('confidence', 0.5)
            text_conf = text_result.get('confidence', 0.5)
            
            # 如果两个模态识别的情感一致，增强置信度
            if audio_emotion == text_emotion:
                fused_emotion = audio_emotion
                fused_confidence = min(1.0, audio_conf * audio_weight + text_conf * text_weight + 0.2)
                consistency = 1.0
            else:
                # 选择置信度更高的情感
                if audio_conf * audio_weight > text_conf * text_weight:
                    fused_emotion = audio_emotion
                    fused_confidence = audio_conf * audio_weight
                else:
                    fused_emotion = text_emotion
                    fused_confidence = text_conf * text_weight
                consistency = 0.5
            
            return {
                'fused_emotion': fused_emotion,
                'fused_confidence': fused_confidence,
                'audio_emotion': audio_emotion,
                'text_emotion': text_emotion,
                'consistency_score': consistency,
                'fusion_method': 'weighted',
                'audio_confidence': audio_conf,
                'text_confidence': text_conf
            }
            
        except Exception as e:
            logger.error(f"加权融合失败: {e}")
            return {
                'fused_emotion': '错误',
                'fused_confidence': 0.0,
                'error': str(e)
            }

if __name__ == "__main__":
    # 测试代码 - 展示自适应融合算法
    fusion = SimpleMultiModalFusion()
    
    print("="*60)
    print("🧠 智能多模态融合算法测试")
    print("="*60)
    
    # 测试场景1: 高置信度音频 vs 低置信度文本（类似实际情况）
    print("\n📊 场景1: 高置信度音频 vs 低置信度文本")
    print("-" * 40)
    audio_result1 = {'top_emotion': '难过', 'confidence': 0.9991}
    text_result1 = {'emotion': '中立', 'confidence': 0.5000}
    
    result1 = fusion.fuse_emotions(audio_result1, text_result1)
    print(f"音频: {result1['audio_emotion']} ({result1['audio_confidence']:.4f})")
    print(f"文本: {result1['text_emotion']} ({result1['text_confidence']:.4f})")
    print(f"融合: {result1['fused_emotion']} ({result1['fused_confidence']:.4f})")
    print(f"策略: {result1['fusion_strategy']}")
    print(f"置信度差距: {result1['confidence_gap']:.4f}")
    
    # 测试场景2: 情感一致的情况
    print("\n📊 场景2: 两模态情感一致")
    print("-" * 40)
    audio_result2 = {'top_emotion': '开心', 'confidence': 0.8}
    text_result2 = {'emotion': '开心', 'confidence': 0.7}
    
    result2 = fusion.fuse_emotions(audio_result2, text_result2)
    print(f"音频: {result2['audio_emotion']} ({result2['audio_confidence']:.4f})")
    print(f"文本: {result2['text_emotion']} ({result2['text_confidence']:.4f})")
    print(f"融合: {result2['fused_emotion']} ({result2['fused_confidence']:.4f})")
    print(f"策略: {result2['fusion_strategy']}")
    print(f"一致性: {result2['consistency_score']:.4f}")
    
    # 测试场景3: 两个模态置信度都较低
    print("\n📊 场景3: 两模态置信度都较低")
    print("-" * 40)
    audio_result3 = {'top_emotion': '生气', 'confidence': 0.55}
    text_result3 = {'emotion': '厌恶', 'confidence': 0.52}
    
    result3 = fusion.fuse_emotions(audio_result3, text_result3)
    print(f"音频: {result3['audio_emotion']} ({result3['audio_confidence']:.4f})")
    print(f"文本: {result3['text_emotion']} ({result3['text_confidence']:.4f})")
    print(f"融合: {result3['fused_emotion']} ({result3['fused_confidence']:.4f})")
    print(f"策略: {result3['fusion_strategy']}")
    print(f"置信度差距: {result3['confidence_gap']:.4f}")
    
    # 测试场景4: 置信度相近但情感不同
    print("\n📊 场景4: 置信度相近但情感不同")
    print("-" * 40)
    audio_result4 = {'top_emotion': '吃惊', 'confidence': 0.75}
    text_result4 = {'emotion': '恐惧', 'confidence': 0.72}
    
    result4 = fusion.fuse_emotions(audio_result4, text_result4)
    print(f"音频: {result4['audio_emotion']} ({result4['audio_confidence']:.4f})")
    print(f"文本: {result4['text_emotion']} ({result4['text_confidence']:.4f})")
    print(f"融合: {result4['fused_emotion']} ({result4['fused_confidence']:.4f})")
    print(f"策略: {result4['fusion_strategy']}")
    print(f"置信度差距: {result4['confidence_gap']:.4f}")
    
    print("\n" + "="*60)
    print("✨ 智能融合算法特点:")
    print("• 根据置信度差距动态选择策略")
    print("• 高置信度模态优先")
    print("• 情感一致时增强置信度")
    print("• 低置信度时采用保守策略")
    print("• 提供详细的融合策略说明")
    print("="*60)