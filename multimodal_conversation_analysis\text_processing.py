#!/usr/bin/env python3
"""
文本处理模块
包含文本情感分析和文本预处理功能
"""

import re
import jieba
import numpy as np
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TextProcessor:
    """文本处理基类"""
    
    def __init__(self):
        self.max_length = 512  # BERT最大输入长度
        
    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        if not text:
            return ""
            
        # 去除多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 去除特殊字符（保留中文、英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9，。！？；：""''（）\s]', '', text)
        
        return text
    
    def segment_text(self, text: str) -> List[str]:
        """中文分词"""
        try:
            words = jieba.lcut(text)
            return [word.strip() for word in words if word.strip()]
        except Exception as e:
            logger.warning(f"分词失败: {e}")
            return [text]

class TextEmotionProcessor(TextProcessor):
    """文本情感分析处理器"""
    
    def __init__(self):
        super().__init__()
        self.model = None
        self.tokenizer = None
        self.emotion_labels = [
            "生气", "厌恶", "恐惧", "开心", "中立", "难过", "吃惊"
        ]
        
        # 初始化情感词典
        self.emotion_dict = {}
        
        # 尝试加载BERT模型，失败则使用规则方法
        self._load_model()
        
        # 确保emotion_dict被初始化
        if not hasattr(self, 'emotion_dict') or not self.emotion_dict:
            self._use_fallback_method()
        
    def _load_model(self):
        """加载中文BERT情感分析模型"""
        try:
            logger.info("正在加载中文BERT模型...")
            
            # 使用transformers库加载预训练模型
            from transformers import AutoTokenizer, AutoModelForSequenceClassification
            import torch
            
            # 使用中文RoBERTa模型
            model_name = "hfl/chinese-roberta-wwm-ext"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            
            # 注意：这里我们先加载预训练模型，后续需要在情感数据上微调
            # 目前先使用一个简化的情感分析方法
            logger.info("✅ 文本模型加载完成（使用规则基础方法）")
            
            # 即使BERT加载成功，我们也使用规则方法作为主要方法
            self._use_fallback_method()
            
        except Exception as e:
            logger.error(f"文本模型加载失败: {e}")
            # 使用备用方案
            self._use_fallback_method()
    
    def _use_fallback_method(self):
        """使用备用的规则基础情感分析方法"""
        logger.info("使用规则基础的情感分析方法")
        
        # 情感词典（简化版）
        self.emotion_dict = {
            "开心": ["高兴", "快乐", "开心", "愉快", "兴奋", "喜悦", "满意", "幸福"],
            "难过": ["难过", "伤心", "痛苦", "悲伤", "沮丧", "失望", "绝望", "忧郁"],
            "生气": ["生气", "愤怒", "恼火", "气愤", "暴躁", "愤慨", "恼怒", "火大"],
            "恐惧": ["害怕", "恐惧", "担心", "紧张", "焦虑", "不安", "惊慌", "畏惧"],
            "吃惊": ["惊讶", "震惊", "吃惊", "意外", "惊奇", "诧异", "惊愕", "惊呆"],
            "厌恶": ["厌恶", "讨厌", "恶心", "反感", "憎恨", "嫌弃", "排斥", "鄙视"],
            "中立": ["还好", "一般", "普通", "正常", "平常", "平静", "冷静", "淡定"]
        }
    
    def _rule_based_emotion_analysis(self, text: str) -> Dict:
        """基于规则的情感分析"""
        if not text:
            return {
                'emotion': '中立',
                'confidence': 0.5,
                'method': 'rule_based'
            }
        
        # 分词
        words = self.segment_text(text)
        
        # 计算各情感得分
        emotion_scores = {emotion: 0 for emotion in self.emotion_labels}
        
        for word in words:
            for emotion, keywords in self.emotion_dict.items():
                if any(keyword in word for keyword in keywords):
                    emotion_scores[emotion] += 1
        
        # 找到最高得分的情感
        if sum(emotion_scores.values()) == 0:
            return {
                'emotion': '中立',
                'confidence': 0.5,
                'method': 'rule_based',
                'all_scores': emotion_scores
            }
        
        max_emotion = max(emotion_scores, key=emotion_scores.get)
        max_score = emotion_scores[max_emotion]
        total_score = sum(emotion_scores.values())
        confidence = max_score / total_score if total_score > 0 else 0.5
        
        return {
            'emotion': max_emotion,
            'confidence': confidence,
            'method': 'rule_based',
            'all_scores': emotion_scores
        }
    
    def analyze_emotion(self, text: str) -> Dict:
        """
        分析文本情感
        
        Args:
            text: 输入文本
            
        Returns:
            情感分析结果字典
        """
        try:
            logger.info(f"正在分析文本情感: {text[:50]}...")
            
            # 预处理文本
            processed_text = self.preprocess_text(text)
            
            if not processed_text:
                return {
                    'emotion': '中立',
                    'confidence': 0.5,
                    'text': text,
                    'processed_text': processed_text
                }
            
            # 使用规则基础方法分析情感
            result = self._rule_based_emotion_analysis(processed_text)
            result['text'] = text
            result['processed_text'] = processed_text
            
            logger.info(f"文本情感分析完成: {result['emotion']} ({result['confidence']:.4f})")
            
            return result
            
        except Exception as e:
            logger.error(f"文本情感分析失败: {e}")
            return {
                'emotion': '错误',
                'confidence': 0.0,
                'text': text,
                'error': str(e)
            }

class TextFeatureExtractor(TextProcessor):
    """文本特征提取器"""
    
    def __init__(self):
        super().__init__()
        self.tokenizer = None
        self.model = None
        self._load_model()
        
    def _load_model(self):
        """加载BERT模型用于特征提取"""
        try:
            from transformers import AutoTokenizer, AutoModel
            import torch
            
            model_name = "hfl/chinese-roberta-wwm-ext"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModel.from_pretrained(model_name)
            
            # 设置为评估模式
            self.model.eval()
            
            logger.info("✅ 文本特征提取模型加载完成")
            
        except Exception as e:
            logger.error(f"文本特征提取模型加载失败: {e}")
            self.tokenizer = None
            self.model = None
    
    def extract_features(self, text: str) -> np.ndarray:
        """
        提取文本特征向量
        
        Args:
            text: 输入文本
            
        Returns:
            特征向量 (768维)
        """
        try:
            if not self.model or not self.tokenizer:
                # 使用简单的词向量作为备用方案
                return self._simple_text_features(text)
            
            import torch
            
            # 预处理文本
            processed_text = self.preprocess_text(text)
            
            # 分词和编码
            inputs = self.tokenizer(
                processed_text,
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding=True
            )
            
            # 提取特征
            with torch.no_grad():
                outputs = self.model(**inputs)
                # 使用[CLS]标记的输出作为句子特征
                features = outputs.last_hidden_state[:, 0, :].squeeze().numpy()
            
            return features
            
        except Exception as e:
            logger.error(f"文本特征提取失败: {e}")
            return self._simple_text_features(text)
    
    def _simple_text_features(self, text: str) -> np.ndarray:
        """简单的文本特征提取（备用方案）"""
        # 使用文本长度、字符频率等简单特征
        features = np.zeros(768)  # 保持与BERT相同的维度
        
        if text:
            # 文本长度特征
            features[0] = len(text) / 100.0  # 归一化
            
            # 字符频率特征
            char_counts = {}
            for char in text:
                char_counts[char] = char_counts.get(char, 0) + 1
            
            # 使用最常见字符的频率作为特征
            if char_counts:
                max_freq = max(char_counts.values())
                features[1] = max_freq / len(text)
        
        return features

if __name__ == "__main__":
    # 测试代码
    processor = TextEmotionProcessor()
    
    # 测试文本
    test_texts = [
        "我今天很高兴！",
        "这让我感到很难过。",
        "我对此感到非常愤怒！",
        "这真是太令人惊讶了！",
        "今天天气不错。"
    ]
    
    print("="*50)
    print("📝 文本情感分析测试")
    print("="*50)
    
    for text in test_texts:
        result = processor.analyze_emotion(text)
        print(f"文本: {text}")
        print(f"情感: {result['emotion']} (置信度: {result['confidence']:.4f})")
        print("-" * 30)