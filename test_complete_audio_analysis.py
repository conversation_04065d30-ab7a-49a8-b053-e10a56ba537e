#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整音频情感分析测试脚本
测试增强版的多说话人情感分析功能，包括：
1. 完整音频分析（不限制片段数量）
2. 说话人独立情感状态跟踪
3. 音频片段切割和精确分析
4. 详细的情感洞察和报告生成
"""

import os
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_analysis_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_complete_audio_analysis():
    """测试完整的音频情感分析功能"""
    print("🎭 完整音频情感分析测试")
    print("=" * 60)
    
    # 测试音频文件
    audio_path = "对话访谈.WAV"
    if not os.path.exists(audio_path):
        print(f"❌ 测试音频文件不存在: {audio_path}")
        return None
    
    try:
        # 导入必要的模块
        from multimodal_conversation_analysis.speaker_diarization import MultiSpeakerEmotionAnalyzer
        from multimodal_conversation_analysis.speaker_emotion_tracker import format_emotion_report
        
        print(f"📁 音频文件: {audio_path}")
        print(f"📊 文件大小: {os.path.getsize(audio_path) / (1024*1024):.2f} MB")
        
        # 初始化分析器，启用所有增强功能
        print(f"\n🔧 初始化分析器...")
        analyzer = MultiSpeakerEmotionAnalyzer(
            analyze_all_segments=True,      # 分析所有片段
            min_segment_duration=0.5,       # 最小片段时长
            max_segment_duration=30.0,      # 最大片段时长
            enable_emotion_tracking=True,   # 启用情感跟踪
            save_detailed_report=True       # 保存详细报告
        )
        
        # 执行完整分析
        print(f"\n🎯 开始完整音频分析...")
        start_time = datetime.now()
        
        result = analyzer.analyze_multi_speaker_audio(audio_path)
        
        end_time = datetime.now()
        analysis_duration = (end_time - start_time).total_seconds()
        
        if 'error' in result:
            print(f"❌ 分析失败: {result['error']}")
            return None
        
        print(f"✅ 分析完成！耗时: {analysis_duration:.2f}秒")
        
        # 显示基本统计信息
        print(f"\n📊 分析统计:")
        print(f"   📁 音频文件: {audio_path}")
        print(f"   ⏱️  音频总时长: {result.get('total_duration', 0):.2f}秒")
        print(f"   👥 说话人数量: {result.get('total_speakers', 0)}")
        print(f"   📋 总片段数: {result.get('total_segments', 0)}")
        print(f"   📊 分析片段数: {result.get('analyzed_segments', 0)}")
        print(f"   ✅ 有效片段数: {result.get('valid_segments', 0)}")
        print(f"   📈 分析覆盖率: {result.get('analysis_coverage', 0):.1%}")
        print(f"   🎯 分析耗时: {analysis_duration:.2f}秒")
        print(f"   ⚡ 处理速度: {result.get('total_duration', 0) / analysis_duration:.2f}x 实时")
        
        # 统计分析质量
        segments = result.get('segment_results', [])
        successful_segments = [s for s in segments if 'error' not in s]
        failed_segments = [s for s in segments if 'error' in s]
        
        print(f"\n📈 分析质量统计:")
        print(f"   ✅ 成功分析: {len(successful_segments)} 个片段 ({len(successful_segments)/len(segments)*100:.1f}%)")
        print(f"   ❌ 分析失败: {len(failed_segments)} 个片段 ({len(failed_segments)/len(segments)*100:.1f}%)")
        
        # 质量分布统计
        if successful_segments:
            quality_counts = {}
            for segment in successful_segments:
                quality = segment.get('segment_quality', {}).get('level', '未知')
                quality_counts[quality] = quality_counts.get(quality, 0) + 1
            
            print(f"   📊 质量分布:")
            for quality, count in quality_counts.items():
                percentage = count / len(successful_segments) * 100
                print(f"      {quality}: {count} 个 ({percentage:.1f}%)")
        
        # 显示说话人情感汇总
        print(f"\n👥 说话人情感汇总:")
        print("-" * 50)
        summary = result.get('summary', {})
        for speaker, info in summary.items():
            print(f"🎭 {speaker}:")
            print(f"   主要情感: {info.get('dominant_emotion', '未知')} ({info.get('dominance_method', '未知方法')})")
            print(f"   情感占比: {info.get('dominance_percentage', 0):.1%}")
            print(f"   平均置信度: {info.get('average_confidence', 0):.3f}")
            print(f"   情感稳定性: {info.get('emotion_stability', 0):.3f}")
            print(f"   总时长: {info.get('total_duration', 0):.2f}秒")
            print(f"   片段数: {info.get('segment_count', 0)}")
            print(f"   情感多样性: {info.get('emotion_diversity', 0):.3f}")
            print(f"   高置信度片段: {info.get('high_confidence_segments', 0)}")
            
            # 情感分布
            emotion_dist = info.get('emotion_distribution', {})
            if emotion_dist:
                print(f"   情感分布: {dict(emotion_dist)}")
            
            # 多种判断方法对比
            by_count = info.get('dominant_by_count', '未知')
            by_confidence = info.get('dominant_by_confidence', '未知')
            by_duration = info.get('dominant_by_duration', '未知')
            if by_count != by_confidence or by_confidence != by_duration:
                print(f"   判断对比: 计数={by_count}, 置信度={by_confidence}, 时长={by_duration}")
            print()
        
        # 显示情感跟踪洞察
        if 'emotion_insights' in result:
            insights = result['emotion_insights']
            print(f"\n🧠 情感分析洞察:")
            print("-" * 50)
            
            # 说话人情感概况
            speaker_profiles = insights.get('speaker_profiles', {})
            for speaker, profile in speaker_profiles.items():
                print(f"👤 {speaker} 情感概况:")
                print(f"   情感稳定性: {profile.get('emotional_stability', 'N/A')}")
                print(f"   主导情感: {profile.get('dominant_emotion', 'N/A')}")
                print(f"   情感变化次数: {profile.get('emotion_changes', 0)}")
                print(f"   情感强度: {profile.get('emotional_intensity', 'N/A')}")
                print()
            
            # 对话整体分析
            conversation_analysis = insights.get('conversation_analysis', {})
            if conversation_analysis:
                print(f"💬 对话整体分析:")
                print(f"   整体情感氛围: {conversation_analysis.get('overall_mood', 'N/A')}")
                print(f"   情感一致性: {conversation_analysis.get('emotional_consistency', 'N/A')}")
                print(f"   互动质量: {conversation_analysis.get('interaction_quality', 'N/A')}")
                print(f"   对话活跃度: {conversation_analysis.get('conversation_activity', 'N/A')}")
                print()
        
        # 显示片段分析详情（智能采样显示）
        if segments:
            print(f"\n📋 片段分析详情:")
            print("-" * 70)
            
            # 智能采样：显示前10个、中间5个、后10个
            display_segments = []
            if len(segments) <= 25:
                display_segments = segments
            else:
                display_segments.extend(segments[:10])  # 前10个
                mid_start = len(segments) // 2 - 2
                display_segments.extend(segments[mid_start:mid_start+5])  # 中间5个
                display_segments.extend(segments[-10:])  # 后10个
            
            for i, segment in enumerate(display_segments):
                if 'error' in segment:
                    print(f"{i+1:2d}. ❌ {segment.get('speaker', '未知')} | "
                          f"{segment.get('start_time', 0):.2f}s-{segment.get('end_time', 0):.2f}s | "
                          f"错误: {segment['error']}")
                else:
                    # 质量指示器
                    quality = segment.get('segment_quality', {})
                    quality_indicator = "🟢" if quality.get('level') == "优秀" else \
                                      "🟡" if quality.get('level') == "良好" else \
                                      "🟠" if quality.get('level') == "一般" else "🔴"
                    
                    print(f"{i+1:2d}. {quality_indicator} {segment.get('speaker', '未知')} | "
                          f"{segment.get('start_time', 0):.2f}s-{segment.get('end_time', 0):.2f}s | "
                          f"情感: {segment.get('fused_emotion', '无')} ({segment.get('fused_confidence', 0):.3f}) | "
                          f"质量: {quality.get('level', 'N/A')}")
                    
                    # 显示转录文本
                    transcription = segment.get('transcription', '').strip()
                    if transcription:
                        if len(transcription) <= 100:
                            print(f"     💬 \"{transcription}\"")
                        else:
                            print(f"     💬 \"{transcription[:100]}...\"")
                    
                    # 显示音频和文本情感对比
                    audio_emotion = segment.get('audio_emotion', '无')
                    text_emotion = segment.get('text_emotion', '无')
                    if audio_emotion != text_emotion:
                        print(f"     🔄 音频: {audio_emotion} | 文本: {text_emotion}")
            
            if len(segments) > 25:
                print(f"     ... (省略中间 {len(segments) - 25} 个片段) ...")
        
        # 保存详细报告
        if result.get('detailed_report_saved'):
            report_path = result.get('detailed_report_path')
            print(f"\n📄 详细报告已保存至: {report_path}")
        
        # 生成格式化报告
        if hasattr(analyzer, 'emotion_tracker') and analyzer.emotion_tracker:
            try:
                formatted_report = format_emotion_report(analyzer.emotion_tracker)
                report_file = f"emotion_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                with open(report_file, 'w', encoding='utf-8') as f:
                    f.write(formatted_report)
                print(f"📋 格式化报告已保存至: {report_file}")
            except Exception as e:
                print(f"⚠️  格式化报告生成失败: {e}")
        
        print(f"\n🎉 完整音频分析测试成功完成！")
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🚀 启动完整音频情感分析测试")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行测试
    result = test_complete_audio_analysis()
    
    if result:
        print(f"\n✅ 测试成功完成！")
    else:
        print(f"\n❌ 测试失败！")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()