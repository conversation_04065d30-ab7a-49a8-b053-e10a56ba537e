#!/usr/bin/env python3
"""
说话人情感状态跟踪器
维护每个说话人的独立情感状态，支持情感变化趋势分析
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class EmotionState:
    """情感状态数据类"""
    emotion: str
    confidence: float
    timestamp: float
    duration: float
    audio_emotion: str = ""
    text_emotion: str = ""
    audio_confidence: float = 0.0
    text_confidence: float = 0.0

@dataclass
class SpeakerProfile:
    """说话人档案"""
    speaker_id: str
    emotion_history: List[EmotionState] = field(default_factory=list)
    dominant_emotions: Dict[str, float] = field(default_factory=dict)
    total_speaking_time: float = 0.0
    segment_count: int = 0
    emotion_transitions: List[Tuple[str, str, float]] = field(default_factory=list)
    
    def add_emotion_state(self, emotion_state: EmotionState):
        """添加新的情感状态"""
        # 检查情感转换
        if self.emotion_history:
            last_emotion = self.emotion_history[-1].emotion
            if last_emotion != emotion_state.emotion:
                self.emotion_transitions.append((
                    last_emotion, 
                    emotion_state.emotion, 
                    emotion_state.timestamp
                ))
        
        self.emotion_history.append(emotion_state)
        self.total_speaking_time += emotion_state.duration
        self.segment_count += 1
        
        # 更新主要情感统计
        if emotion_state.emotion not in self.dominant_emotions:
            self.dominant_emotions[emotion_state.emotion] = 0.0
        self.dominant_emotions[emotion_state.emotion] += emotion_state.duration
    
    def get_dominant_emotion(self) -> Tuple[str, float]:
        """获取主要情感"""
        if not self.dominant_emotions:
            return "未知", 0.0
        
        dominant = max(self.dominant_emotions.items(), key=lambda x: x[1])
        percentage = dominant[1] / self.total_speaking_time if self.total_speaking_time > 0 else 0.0
        return dominant[0], percentage
    
    def get_emotion_stability(self) -> float:
        """计算情感稳定性（转换次数越少越稳定）"""
        if self.segment_count <= 1:
            return 1.0
        
        transition_rate = len(self.emotion_transitions) / (self.segment_count - 1)
        stability = max(0.0, 1.0 - transition_rate)
        return stability
    
    def get_average_confidence(self) -> float:
        """获取平均置信度"""
        if not self.emotion_history:
            return 0.0
        
        confidences = [state.confidence for state in self.emotion_history]
        return np.mean(confidences)
    
    def get_emotion_timeline(self) -> List[Dict]:
        """获取情感时间线"""
        timeline = []
        for state in self.emotion_history:
            timeline.append({
                'timestamp': state.timestamp,
                'emotion': state.emotion,
                'confidence': state.confidence,
                'duration': state.duration,
                'audio_emotion': state.audio_emotion,
                'text_emotion': state.text_emotion
            })
        return timeline

class SpeakerEmotionTracker:
    """说话人情感状态跟踪器"""
    
    def __init__(self):
        self.speakers: Dict[str, SpeakerProfile] = {}
        self.analysis_start_time = None
        self.total_analysis_duration = 0.0
        
    def add_speaker_emotion(self, speaker_id: str, emotion: str, confidence: float,
                          timestamp: float, duration: float, 
                          audio_emotion: str = "", text_emotion: str = "",
                          audio_confidence: float = 0.0, text_confidence: float = 0.0):
        """添加说话人情感状态"""
        
        if speaker_id not in self.speakers:
            self.speakers[speaker_id] = SpeakerProfile(speaker_id=speaker_id)
        
        emotion_state = EmotionState(
            emotion=emotion,
            confidence=confidence,
            timestamp=timestamp,
            duration=duration,
            audio_emotion=audio_emotion,
            text_emotion=text_emotion,
            audio_confidence=audio_confidence,
            text_confidence=text_confidence
        )
        
        self.speakers[speaker_id].add_emotion_state(emotion_state)
        logger.debug(f"添加情感状态: {speaker_id} - {emotion} ({confidence:.3f}) at {timestamp:.2f}s")
    
    def get_speaker_summary(self, speaker_id: str) -> Optional[Dict]:
        """获取说话人情感汇总"""
        if speaker_id not in self.speakers:
            return None
        
        profile = self.speakers[speaker_id]
        dominant_emotion, dominance_percentage = profile.get_dominant_emotion()
        
        return {
            'speaker_id': speaker_id,
            'dominant_emotion': dominant_emotion,
            'dominance_percentage': dominance_percentage,
            'average_confidence': profile.get_average_confidence(),
            'emotion_stability': profile.get_emotion_stability(),
            'total_speaking_time': profile.total_speaking_time,
            'segment_count': profile.segment_count,
            'emotion_distribution': dict(profile.dominant_emotions),
            'emotion_transitions': profile.emotion_transitions,
            'emotion_timeline': profile.get_emotion_timeline()
        }
    
    def get_all_speakers_summary(self) -> Dict[str, Dict]:
        """获取所有说话人的情感汇总"""
        summary = {}
        for speaker_id in self.speakers:
            summary[speaker_id] = self.get_speaker_summary(speaker_id)
        return summary
    
    def get_conversation_insights(self) -> Dict:
        """获取对话整体洞察"""
        if not self.speakers:
            return {}
        
        # 计算整体统计
        total_speakers = len(self.speakers)
        all_emotions = []
        all_confidences = []
        total_segments = 0
        total_duration = 0.0
        
        for profile in self.speakers.values():
            total_segments += profile.segment_count
            total_duration += profile.total_speaking_time
            for state in profile.emotion_history:
                all_emotions.append(state.emotion)
                all_confidences.append(state.confidence)
        
        # 计算主要情感分布
        emotion_counts = {}
        for emotion in all_emotions:
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        # 计算情感多样性
        emotion_diversity = len(set(all_emotions)) / len(all_emotions) if all_emotions else 0.0
        
        # 找出最活跃的说话人
        most_active_speaker = max(self.speakers.values(), 
                                key=lambda p: p.total_speaking_time).speaker_id
        
        # 找出情感最稳定的说话人
        most_stable_speaker = max(self.speakers.values(),
                                key=lambda p: p.get_emotion_stability()).speaker_id
        
        return {
            'total_speakers': total_speakers,
            'total_segments': total_segments,
            'total_duration': total_duration,
            'average_confidence': np.mean(all_confidences) if all_confidences else 0.0,
            'emotion_distribution': emotion_counts,
            'emotion_diversity': emotion_diversity,
            'most_active_speaker': most_active_speaker,
            'most_stable_speaker': most_stable_speaker,
            'speakers_summary': self.get_all_speakers_summary()
        }
    
    def detect_emotion_patterns(self) -> Dict:
        """检测情感模式"""
        patterns = {
            'frequent_transitions': [],  # 频繁转换的说话人
            'stable_speakers': [],       # 情感稳定的说话人
            'dominant_emotions': {},     # 每个说话人的主要情感
            'synchronous_emotions': []   # 同步情感变化
        }
        
        for speaker_id, profile in self.speakers.items():
            # 检测频繁转换
            if profile.get_emotion_stability() < 0.5:
                patterns['frequent_transitions'].append({
                    'speaker': speaker_id,
                    'stability': profile.get_emotion_stability(),
                    'transitions': len(profile.emotion_transitions)
                })
            
            # 检测稳定说话人
            if profile.get_emotion_stability() > 0.8:
                patterns['stable_speakers'].append({
                    'speaker': speaker_id,
                    'stability': profile.get_emotion_stability(),
                    'dominant_emotion': profile.get_dominant_emotion()[0]
                })
            
            # 记录主要情感
            dominant_emotion, _ = profile.get_dominant_emotion()
            patterns['dominant_emotions'][speaker_id] = dominant_emotion
        
        return patterns
    
    def export_detailed_report(self) -> Dict:
        """导出详细报告"""
        return {
            'analysis_metadata': {
                'total_speakers': len(self.speakers),
                'analysis_duration': self.total_analysis_duration,
                'timestamp': datetime.now().isoformat()
            },
            'conversation_insights': self.get_conversation_insights(),
            'emotion_patterns': self.detect_emotion_patterns(),
            'speakers_detailed': self.get_all_speakers_summary()
        }

def format_emotion_report(tracker: SpeakerEmotionTracker) -> str:
    """格式化情感分析报告"""
    insights = tracker.get_conversation_insights()
    patterns = tracker.detect_emotion_patterns()
    
    report = []
    report.append("🎭 说话人情感状态分析报告")
    report.append("=" * 60)
    
    # 基本统计
    report.append(f"👥 总说话人数: {insights.get('total_speakers', 0)}")
    report.append(f"📊 总片段数: {insights.get('total_segments', 0)}")
    report.append(f"⏱️ 总时长: {insights.get('total_duration', 0):.2f}秒")
    report.append(f"🎯 平均置信度: {insights.get('average_confidence', 0):.3f}")
    report.append(f"🌈 情感多样性: {insights.get('emotion_diversity', 0):.3f}")
    
    # 整体情感分布
    report.append(f"\n📈 整体情感分布:")
    emotion_dist = insights.get('emotion_distribution', {})
    for emotion, count in sorted(emotion_dist.items(), key=lambda x: x[1], reverse=True):
        percentage = count / insights.get('total_segments', 1) * 100
        report.append(f"   {emotion}: {count}次 ({percentage:.1f}%)")
    
    # 说话人详细信息
    report.append(f"\n👤 说话人详细分析:")
    report.append("-" * 40)
    
    speakers_summary = insights.get('speakers_summary', {})
    for speaker_id, summary in speakers_summary.items():
        report.append(f"\n🗣️ {speaker_id}:")
        report.append(f"   主要情感: {summary['dominant_emotion']} ({summary['dominance_percentage']:.1%})")
        report.append(f"   情感稳定性: {summary['emotion_stability']:.3f}")
        report.append(f"   平均置信度: {summary['average_confidence']:.3f}")
        report.append(f"   说话时长: {summary['total_speaking_time']:.2f}秒")
        report.append(f"   片段数量: {summary['segment_count']}")
        report.append(f"   情感转换: {len(summary['emotion_transitions'])}次")
    
    # 情感模式
    report.append(f"\n🔍 情感模式分析:")
    report.append("-" * 40)
    
    if patterns['stable_speakers']:
        report.append("✅ 情感稳定的说话人:")
        for speaker_info in patterns['stable_speakers']:
            report.append(f"   {speaker_info['speaker']}: {speaker_info['dominant_emotion']} "
                         f"(稳定性: {speaker_info['stability']:.3f})")
    
    if patterns['frequent_transitions']:
        report.append("⚡ 情感变化频繁的说话人:")
        for speaker_info in patterns['frequent_transitions']:
            report.append(f"   {speaker_info['speaker']}: {speaker_info['transitions']}次转换 "
                         f"(稳定性: {speaker_info['stability']:.3f})")
    
    return "\n".join(report)