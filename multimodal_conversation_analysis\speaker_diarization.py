#!/usr/bin/env python3
"""
说话人分离模块
实现多说话人识别、分离和时间戳标注功能
"""

import os
import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
import warnings
from datetime import timedelta
import tempfile

# 导入情感状态跟踪器
from .speaker_emotion_tracker import SpeakerEmotionTracker, format_emotion_report

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 抑制一些警告
warnings.filterwarnings("ignore", category=UserWarning)

# ==================== 配置区域 ====================
# 在这里直接设置您的Hugging Face token
# 替换下面的 "your_token_here" 为您的实际token
HUGGINGFACE_TOKEN = "*************************************"

# 如果您不想硬编码token，可以设置为 None
# HUGGINGFACE_TOKEN = None
# ================================================

class SpeakerDiarizationProcessor:
    """说话人分离处理器"""
    
    def __init__(self, use_auth_token: Optional[str] = None):
        """
        初始化说话人分离处理器
        
        Args:
            use_auth_token: Hugging Face认证令牌（可选）
                          如果为None，将使用硬编码的HUGGINGFACE_TOKEN
        """
        self.pipeline = None
        
        # 优先级：传入的token > 硬编码token > 环境变量
        if use_auth_token:
            self.use_auth_token = use_auth_token
        elif HUGGINGFACE_TOKEN and HUGGINGFACE_TOKEN != "your_token_here":
            self.use_auth_token = HUGGINGFACE_TOKEN
            logger.info("使用硬编码的Hugging Face token")
        else:
            # 尝试从环境变量获取
            self.use_auth_token = os.environ.get('HF_TOKEN') or os.environ.get('HUGGINGFACE_HUB_TOKEN')
            if self.use_auth_token:
                logger.info("使用环境变量中的Hugging Face token")
            else:
                logger.warning("未找到Hugging Face token，将使用简单的分离方法")
        
        self._load_model()
        
    def _load_model(self):
        """加载pyannote.audio说话人分离模型"""
        try:
            logger.info("正在加载说话人分离模型...")
            
            # 尝试导入pyannote.audio
            try:
                from pyannote.audio import Pipeline
                
                # 加载预训练的说话人分离模型
                self.pipeline = Pipeline.from_pretrained(
                    "pyannote/speaker-diarization-3.1",
                    use_auth_token=self.use_auth_token
                )
                
                # 设置设备
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                self.pipeline = self.pipeline.to(device)
                
                logger.info(f"✅ 说话人分离模型加载完成 (设备: {device})")
                
            except ImportError:
                logger.warning("pyannote.audio未安装，使用简单的基于能量的分离方法")
                self.pipeline = None
                
        except Exception as e:
            logger.error(f"说话人分离模型加载失败: {e}")
            logger.info("将使用简单的基于能量的分离方法")
            self.pipeline = None
    
    def diarize_audio(self, audio_path: str, min_speakers: int = 1, max_speakers: int = 10) -> Dict:
        """
        对音频进行说话人分离
        
        Args:
            audio_path: 音频文件路径
            min_speakers: 最小说话人数量
            max_speakers: 最大说话人数量
            
        Returns:
            说话人分离结果
        """
        try:
            logger.info(f"开始说话人分离: {audio_path}")
            
            if self.pipeline is not None:
                return self._pyannote_diarization(audio_path, min_speakers, max_speakers)
            else:
                return self._simple_energy_based_diarization(audio_path)
                
        except Exception as e:
            logger.error(f"说话人分离失败: {e}")
            return {
                'error': str(e),
                'speakers': [],
                'segments': [],
                'num_speakers': 0
            }
    
    def _pyannote_diarization(self, audio_path: str, min_speakers: int, max_speakers: int) -> Dict:
        """使用pyannote.audio进行说话人分离"""
        try:
            # 执行说话人分离
            diarization = self.pipeline(
                audio_path,
                min_speakers=min_speakers,
                max_speakers=max_speakers
            )
            
            # 解析结果
            speakers = set()
            segments = []
            
            for turn, _, speaker in diarization.itertracks(yield_label=True):
                speakers.add(speaker)
                segments.append({
                    'speaker': speaker,
                    'start_time': turn.start,
                    'end_time': turn.end,
                    'duration': turn.end - turn.start,
                    'start_time_str': str(timedelta(seconds=turn.start)),
                    'end_time_str': str(timedelta(seconds=turn.end))
                })
            
            # 按时间排序
            segments.sort(key=lambda x: x['start_time'])
            
            logger.info(f"说话人分离完成: 检测到 {len(speakers)} 个说话人，{len(segments)} 个片段")
            
            return {
                'speakers': list(speakers),
                'segments': segments,
                'num_speakers': len(speakers),
                'total_segments': len(segments),
                'method': 'pyannote',
                'audio_path': audio_path
            }
            
        except Exception as e:
            logger.error(f"pyannote说话人分离失败: {e}")
            raise
    
    def _simple_energy_based_diarization(self, audio_path: str) -> Dict:
        """简单的基于能量的说话人分离（备用方法）"""
        try:
            import librosa
            import soundfile as sf
            
            logger.info("使用简单的基于能量的说话人分离方法")
            
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 计算短时能量
            frame_length = int(0.025 * sr)  # 25ms窗口
            hop_length = int(0.01 * sr)     # 10ms步长
            
            # 计算RMS能量
            rms = librosa.feature.rms(
                y=audio, 
                frame_length=frame_length, 
                hop_length=hop_length
            )[0]
            
            # 计算时间轴
            times = librosa.frames_to_time(
                np.arange(len(rms)), 
                sr=sr, 
                hop_length=hop_length
            )
            
            # 简单的语音活动检测
            threshold = np.percentile(rms, 30)  # 使用30%分位数作为阈值
            speech_frames = rms > threshold
            
            # 找到语音片段
            segments = []
            in_speech = False
            start_time = 0
            
            for i, is_speech in enumerate(speech_frames):
                if is_speech and not in_speech:
                    # 语音开始
                    start_time = times[i]
                    in_speech = True
                elif not is_speech and in_speech:
                    # 语音结束
                    end_time = times[i]
                    if end_time - start_time > 0.5:  # 至少0.5秒
                        segments.append({
                            'speaker': 'Speaker_1',  # 简单方法只能检测一个说话人
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': end_time - start_time,
                            'start_time_str': str(timedelta(seconds=start_time)),
                            'end_time_str': str(timedelta(seconds=end_time))
                        })
                    in_speech = False
            
            # 处理最后一个片段
            if in_speech:
                end_time = times[-1]
                if end_time - start_time > 0.5:
                    segments.append({
                        'speaker': 'Speaker_1',
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'start_time_str': str(timedelta(seconds=start_time)),
                        'end_time_str': str(timedelta(seconds=end_time))
                    })
            
            logger.info(f"简单说话人分离完成: 检测到 {len(segments)} 个语音片段")
            
            return {
                'speakers': ['Speaker_1'],
                'segments': segments,
                'num_speakers': 1,
                'total_segments': len(segments),
                'method': 'energy_based',
                'audio_path': audio_path,
                'note': '简单方法：仅检测语音活动，无法区分多个说话人'
            }
            
        except Exception as e:
            logger.error(f"简单说话人分离失败: {e}")
            raise

class MultiSpeakerEmotionAnalyzer:
    """多说话人情感分析器 - 增强版本，支持完整音频分析和情感状态跟踪"""
    
    def __init__(self, 
                 use_auth_token: Optional[str] = None,
                 analyze_all_segments: bool = True,
                 min_segment_duration: float = 0.5,
                 max_segment_duration: float = 30.0,
                 enable_emotion_tracking: bool = True,
                 save_detailed_report: bool = True):
        """
        初始化多说话人情感分析器
        
        Args:
            use_auth_token: Hugging Face认证令牌
            analyze_all_segments: 是否分析所有片段
            min_segment_duration: 最小片段时长（秒）
            max_segment_duration: 最大片段时长（秒）
            enable_emotion_tracking: 是否启用情感跟踪
            save_detailed_report: 是否保存详细报告
        """
        self.diarization_processor = SpeakerDiarizationProcessor(use_auth_token=use_auth_token)
        
        # 延迟导入其他处理器，避免循环导入
        self.audio_processor = None
        self.text_processor = None
        self.fusion_processor = None
        
        # 初始化情感状态跟踪器
        self.emotion_tracker = SpeakerEmotionTracker()
        
        # 分析配置
        self.config = {
            'analyze_all_segments': analyze_all_segments,
            'min_segment_duration': min_segment_duration,
            'max_segment_duration': max_segment_duration,
            'enable_emotion_tracking': enable_emotion_tracking,
            'save_detailed_report': save_detailed_report
        }
        
    def _init_processors(self):
        """延迟初始化处理器"""
        if self.audio_processor is None:
            # 尝试多种导入方式以确保兼容性
            try:
                # 首先尝试相对导入
                from .audio_processing import IntegratedAudioProcessor
                from .text_processing import TextEmotionProcessor
                from .multimodal_fusion import SimpleMultiModalFusion
            except ImportError:
                try:
                    # 尝试绝对导入
                    from audio_processing import IntegratedAudioProcessor
                    from text_processing import TextEmotionProcessor
                    from multimodal_fusion import SimpleMultiModalFusion
                except ImportError:
                    try:
                        # 添加当前目录到路径并导入
                        import sys
                        import os
                        current_dir = os.path.dirname(os.path.abspath(__file__))
                        if current_dir not in sys.path:
                            sys.path.insert(0, current_dir)
                        from audio_processing import IntegratedAudioProcessor
                        from text_processing import TextEmotionProcessor
                        from multimodal_fusion import SimpleMultiModalFusion
                    except ImportError as e:
                        # 如果所有导入都失败，提供详细的错误信息
                        raise ImportError(
                            f"无法导入必需的模块。请确保以下文件存在于当前目录中：\n"
                            f"- audio_processing.py\n"
                            f"- text_processing.py\n"
                            f"- multimodal_fusion.py\n"
                            f"原始错误: {e}"
                        )
            
            self.audio_processor = IntegratedAudioProcessor()
            self.text_processor = TextEmotionProcessor()
            self.fusion_processor = SimpleMultiModalFusion()
    
    def analyze_multi_speaker_audio(self, audio_path: str, 
                                  min_speakers: int = 1, 
                                  max_speakers: int = 10,
                                  analyze_all_segments: bool = None) -> Dict:
        """
        分析多说话人音频的情感 - 增强版本
        
        Args:
            audio_path: 音频文件路径
            min_speakers: 最小说话人数量
            max_speakers: 最大说话人数量
            analyze_all_segments: 是否分析所有片段（None时使用配置）
            
        Returns:
            包含详细情感分析和跟踪信息的字典
        """
        logger.info(f"开始增强版多说话人情感分析: {audio_path}")
        
        # 重置情感跟踪器
        self.emotion_tracker = SpeakerEmotionTracker()
        
        # 确定是否分析所有片段
        if analyze_all_segments is None:
            analyze_all_segments = self.config['analyze_all_segments']
        
        try:
            # 1. 初始化处理器
            self._init_processors()
            
            # 2. 执行说话人分离
            logger.info("执行说话人分离...")
            diarization_result = self.diarization_processor.diarize_audio(
                audio_path, min_speakers=min_speakers, max_speakers=max_speakers
            )
            
            if 'error' in diarization_result:
                return {'error': f"说话人分离失败: {diarization_result['error']}"}
            
            segments = diarization_result.get('segments', [])
            total_segments = len(segments)
            
            logger.info(f"检测到 {diarization_result.get('num_speakers', 0)} 个说话人，"
                       f"共 {total_segments} 个片段")
            
            # 3. 过滤片段（根据时长）
            valid_segments = []
            filtered_segments = []
            
            logger.info(f"开始过滤片段，时长范围: {self.config['min_segment_duration']:.1f}s - {self.config['max_segment_duration']:.1f}s")
            
            for segment in segments:
                duration = segment['end_time'] - segment['start_time']
                if (self.config['min_segment_duration'] <= duration <= 
                    self.config['max_segment_duration']):
                    valid_segments.append(segment)
                else:
                    filtered_segments.append({
                        'segment': segment,
                        'duration': duration,
                        'reason': '太短' if duration < self.config['min_segment_duration'] else '太长'
                    })
                    logger.debug(f"跳过片段 {segment['speaker']} "
                               f"({duration:.2f}s): 时长不符合要求 ({'太短' if duration < self.config['min_segment_duration'] else '太长'})")
            
            logger.info(f"片段过滤完成: 有效 {len(valid_segments)} 个，过滤 {len(filtered_segments)} 个")
            
            # 统计过滤原因
            if filtered_segments:
                filter_stats = {}
                for item in filtered_segments:
                    reason = item['reason']
                    filter_stats[reason] = filter_stats.get(reason, 0) + 1
                logger.info(f"过滤统计: {dict(filter_stats)}")
                
                # 显示一些被过滤的片段示例
                for reason in filter_stats:
                    examples = [item for item in filtered_segments if item['reason'] == reason][:3]
                    for example in examples:
                        seg = example['segment']
                        logger.debug(f"过滤示例({reason}): {seg['speaker']} "
                                   f"{seg['start_time']:.2f}s-{seg['end_time']:.2f}s "
                                   f"({example['duration']:.2f}s)")
            
            # 4. 决定分析的片段
            if analyze_all_segments:
                segments_to_analyze = valid_segments
                logger.info(f"将分析所有 {len(segments_to_analyze)} 个有效片段")
            else:
                # 如果不分析所有片段，取前几个作为示例
                max_sample_segments = 10
                segments_to_analyze = valid_segments[:max_sample_segments]
                logger.info(f"将分析前 {len(segments_to_analyze)} 个片段作为示例")
            
            # 5. 分析每个片段
            segment_results = []
            speaker_emotions = {}
            
            logger.info("开始逐片段情感分析...")
            for i, segment in enumerate(segments_to_analyze, 1):
                speaker = segment['speaker']
                start_time = segment['start_time']
                end_time = segment['end_time']
                duration = end_time - start_time
                
                logger.info(f"分析片段 {i}/{len(segments_to_analyze)}: "
                           f"{speaker} ({start_time:.2f}s - {end_time:.2f}s)")
                
                # 分析单个片段
                segment_result = self._analyze_segment_enhanced(
                    audio_path, speaker, start_time, end_time
                )
                
                segment_results.append(segment_result)
                
                # 更新说话人情感汇总
                if speaker not in speaker_emotions:
                    speaker_emotions[speaker] = []
                speaker_emotions[speaker].append(segment_result)
                
                # 添加到情感跟踪器
                if self.config['enable_emotion_tracking'] and 'error' not in segment_result:
                    self.emotion_tracker.add_speaker_emotion(
                        speaker_id=speaker,
                        emotion=segment_result.get('fused_emotion', '中立'),
                        confidence=segment_result.get('fused_confidence', 0.0),
                        timestamp=start_time,
                        duration=duration,
                        audio_emotion=segment_result.get('audio_emotion', ''),
                        text_emotion=segment_result.get('text_emotion', ''),
                        audio_confidence=segment_result.get('audio_confidence', 0.0),
                        text_confidence=segment_result.get('text_confidence', 0.0)
                    )
            
            # 6. 生成汇总和洞察
            logger.info("生成分析汇总...")
            
            # 传统汇总（保持兼容性）
            traditional_summary = self._summarize_speaker_emotions(speaker_emotions)
            
            # 增强的情感洞察
            emotion_insights = self.emotion_tracker.get_conversation_insights()
            emotion_patterns = self.emotion_tracker.detect_emotion_patterns()
            
            # 7. 构建结果
            # 计算音频总时长
            total_duration = 0.0
            if segments:
                # 从最后一个片段的结束时间获取总时长
                total_duration = max(segment['end_time'] for segment in segments)
            elif 'duration' in diarization_result:
                total_duration = diarization_result['duration']
            
            result = {
                'audio_path': audio_path,
                'total_speakers': diarization_result.get('num_speakers', 0),
                'total_segments': total_segments,
                'analyzed_segments': len(segments_to_analyze),
                'valid_segments': len(valid_segments),
                'total_duration': total_duration,
                'analysis_coverage': len(segments_to_analyze) / total_segments if total_segments > 0 else 0.0,
                
                # 传统结果（保持兼容性）
                'summary': traditional_summary,
                'segment_results': segment_results,
                
                # 增强的情感分析结果
                'emotion_insights': emotion_insights,
                'emotion_patterns': emotion_patterns,
                'speakers_detailed': self.emotion_tracker.get_all_speakers_summary(),
                
                # 元数据
                'analysis_config': self.config.copy(),
                'diarization_result': diarization_result
            }
            
            # 8. 保存详细报告
            if self.config['save_detailed_report']:
                try:
                    from datetime import datetime
                    
                    # 生成报告内容
                    report_content = format_emotion_report(self.emotion_tracker)
                    result['emotion_report'] = report_content
                    
                    # 保存到文件
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    report_filename = f"detailed_emotion_analysis_{timestamp}.txt"
                    
                    with open(report_filename, 'w', encoding='utf-8') as f:
                        f.write("=" * 80 + "\n")
                        f.write("详细情感分析报告\n")
                        f.write("=" * 80 + "\n")
                        f.write(f"音频文件: {audio_path}\n")
                        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"总说话人数: {result['total_speakers']}\n")
                        f.write(f"总片段数: {result['total_segments']}\n")
                        f.write(f"分析片段数: {result['analyzed_segments']}\n")
                        f.write(f"分析覆盖率: {result['analysis_coverage']:.2%}\n")
                        f.write("\n" + "=" * 80 + "\n")
                        f.write(report_content)
                        f.write("\n" + "=" * 80 + "\n")
                        f.write("分析配置:\n")
                        for key, value in self.config.items():
                            f.write(f"  {key}: {value}\n")
                    
                    result['detailed_report_saved'] = True
                    result['detailed_report_path'] = os.path.abspath(report_filename)
                    logger.info(f"详细报告已保存至: {report_filename}")
                    
                except Exception as e:
                    logger.warning(f"保存详细报告失败: {e}")
                    result['detailed_report_saved'] = False
            
            logger.info(f"多说话人情感分析完成！分析了 {len(segments_to_analyze)}/{total_segments} 个片段")
            return result
            
        except Exception as e:
            logger.error(f"多说话人情感分析失败: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}
    
    def _extract_audio_segment(self, audio_path: str, start_time: float, 
                              end_time: float, speaker: str) -> str:
        """切割音频片段到临时文件"""
        try:
            import tempfile
            import soundfile as sf
            import numpy as np
            
            # 读取原始音频
            audio, sr = sf.read(audio_path)
            
            # 转换为单声道
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)
            
            # 计算样本索引
            start_sample = int(start_time * sr)
            end_sample = int(end_time * sr)
            
            # 确保索引在有效范围内
            start_sample = max(0, start_sample)
            end_sample = min(len(audio), end_sample)
            
            if start_sample >= end_sample:
                logger.warning(f"无效的时间范围: {start_time}s - {end_time}s")
                return None
            
            # 切割音频片段
            segment_audio = audio[start_sample:end_sample]
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_filename = f"segment_{speaker}_{start_time:.2f}_{end_time:.2f}.wav"
            temp_path = os.path.join(temp_dir, temp_filename)
            
            # 保存音频片段
            sf.write(temp_path, segment_audio, sr)
            
            logger.debug(f"音频片段切割成功: {temp_path}")
            return temp_path
                
        except Exception as e:
            logger.warning(f"音频切割异常: {e}")
            return None

    def _analyze_segment(self, audio_path: str, speaker: str, 
                        start_time: float, end_time: float) -> Dict:
        """增强版片段分析，支持音频切割和更详细的情感分析"""
        try:
            duration = end_time - start_time
            logger.debug(f"分析片段: {speaker} ({start_time:.2f}s - {end_time:.2f}s, {duration:.2f}s)")
            
            # 1. 切割音频片段
            segment_audio_path = self._extract_audio_segment(audio_path, start_time, end_time, speaker)
            
            # 2. 音频情感分析
            if segment_audio_path and os.path.exists(segment_audio_path):
                # 使用切割的音频片段进行分析
                audio_result = self.audio_processor.process_audio(segment_audio_path)
                
                # 清理临时文件
                try:
                    os.remove(segment_audio_path)
                except:
                    pass
            else:
                # 如果切割失败，使用原始音频（降级处理）
                logger.warning(f"音频切割失败，使用原始音频分析片段 {speaker}")
                audio_result = self.audio_processor.process_audio(audio_path)
            
            if 'error' in audio_result:
                logger.warning(f"音频情感分析失败: {audio_result['error']}")
                audio_emotion = {"top_emotion": "未知", "confidence": 0.0}
            else:
                audio_emotion = audio_result.get('emotion', {})
            
            # 3. 获取转录文本
            transcription = audio_result.get('transcription', {})
            text_content = transcription.get('text', '').strip()
            
            # 4. 文本情感分析
            if text_content:
                try:
                    text_result = self.text_processor.analyze_emotion(text_content)
                    if 'error' in text_result:
                        logger.warning(f"文本情感分析失败: {text_result['error']}")
                        text_result = {"emotion": "中立", "confidence": 0.0}
                except Exception as e:
                    logger.warning(f"文本情感分析异常: {e}")
                    text_result = {"emotion": "中立", "confidence": 0.0}
            else:
                text_result = {"emotion": "中立", "confidence": 0.0}
                logger.debug(f"片段 {speaker} 无转录文本")
            
            # 5. 多模态融合
            try:
                audio_fusion_input = {
                    'top_emotion': audio_emotion.get('top_emotion', '中立'),
                    'confidence': audio_emotion.get('confidence', 0.0)
                }
                
                text_fusion_input = {
                    'emotion': text_result.get('emotion', '中立'),
                    'confidence': text_result.get('confidence', 0.0)
                }
                
                fusion_result = self.fusion_processor.fuse_emotions(
                    audio_fusion_input, text_fusion_input
                )
                
                if 'error' in fusion_result:
                    logger.warning(f"多模态融合失败: {fusion_result['error']}")
                    fused_emotion = audio_emotion.get('top_emotion', '中立')
                    fused_confidence = audio_emotion.get('confidence', 0.0)
                else:
                    fused_emotion = fusion_result.get('fused_emotion', '中立')
                    fused_confidence = fusion_result.get('fused_confidence', 0.0)
                    
            except Exception as e:
                logger.warning(f"多模态融合异常: {e}")
                fused_emotion = audio_emotion.get('top_emotion', '中立')
                fused_confidence = audio_emotion.get('confidence', 0.0)
            
            # 6. 构建结果
            result = {
                'speaker': speaker,
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,
                'transcription': text_content,
                'audio_emotion': audio_emotion.get('top_emotion', '中立'),
                'text_emotion': text_result.get('emotion', '中立'),
                'fused_emotion': fused_emotion,
                'audio_confidence': audio_emotion.get('confidence', 0.0),
                'text_confidence': text_result.get('confidence', 0.0),
                'fused_confidence': fused_confidence,
                
                # 额外的详细信息
                'audio_emotions_all': audio_emotion.get('all_emotions', []),
                'text_analysis_details': text_result,
                'has_transcription': bool(text_content),
                'segment_quality': self._assess_segment_quality(audio_emotion, text_result, duration)
            }
            
            logger.debug(f"片段分析完成: {speaker} - {fused_emotion} ({fused_confidence:.3f})")
            return result
            
        except Exception as e:
            logger.error(f"增强片段分析失败: {e}")
            return {
                'speaker': speaker,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time,
                'error': str(e)
            }
    
    def _assess_segment_quality(self, audio_emotion: Dict, text_result: Dict, duration: float) -> Dict:
        """评估片段分析质量"""
        quality_score = 0.0
        quality_factors = []
        
        # 音频质量评估
        audio_confidence = audio_emotion.get('confidence', 0.0)
        if audio_confidence > 0.7:
            quality_score += 0.4
            quality_factors.append("高音频置信度")
        elif audio_confidence > 0.5:
            quality_score += 0.2
            quality_factors.append("中等音频置信度")
        
        # 文本质量评估
        text_confidence = text_result.get('confidence', 0.0)
        if text_confidence > 0.7:
            quality_score += 0.4
            quality_factors.append("高文本置信度")
        elif text_confidence > 0.5:
            quality_score += 0.2
            quality_factors.append("中等文本置信度")
        
        # 时长质量评估
        if 2.0 <= duration <= 10.0:
            quality_score += 0.2
            quality_factors.append("理想时长")
        elif 1.0 <= duration <= 15.0:
            quality_score += 0.1
            quality_factors.append("可接受时长")
        
        # 质量等级
        if quality_score >= 0.8:
            quality_level = "优秀"
        elif quality_score >= 0.6:
            quality_level = "良好"
        elif quality_score >= 0.4:
            quality_level = "一般"
        else:
            quality_level = "较差"
        
        return {
            'score': quality_score,
            'level': quality_level,
            'factors': quality_factors,
            'audio_confidence': audio_confidence,
            'text_confidence': text_confidence,
            'duration': duration
        }
    
    def _analyze_segment_enhanced(self, audio_path: str, speaker: str, 
                                start_time: float, end_time: float) -> Dict:
        """增强版片段分析，支持音频切割和更详细的情感分析"""
        try:
            duration = end_time - start_time
            logger.debug(f"分析片段: {speaker} ({start_time:.2f}s - {end_time:.2f}s, {duration:.2f}s)")
            
            # 1. 切割音频片段
            segment_audio_path = self._extract_audio_segment(audio_path, start_time, end_time, speaker)
            
            # 2. 音频情感分析
            if segment_audio_path and os.path.exists(segment_audio_path):
                # 使用切割的音频片段进行分析
                audio_result = self.audio_processor.process_audio(segment_audio_path)
                
                # 清理临时文件
                try:
                    os.remove(segment_audio_path)
                except:
                    pass
            else:
                # 如果切割失败，使用原始音频（降级处理）
                logger.warning(f"音频切割失败，使用原始音频分析片段 {speaker}")
                audio_result = self.audio_processor.process_audio(audio_path)
            
            if 'error' in audio_result:
                logger.warning(f"音频情感分析失败: {audio_result['error']}")
                audio_emotion = {"top_emotion": "未知", "confidence": 0.0}
            else:
                audio_emotion = audio_result.get('emotion', {})
            
            # 3. 获取转录文本
            transcription = audio_result.get('transcription', {})
            text_content = transcription.get('text', '').strip()
            
            # 4. 文本情感分析
            if text_content:
                try:
                    text_result = self.text_processor.analyze_emotion(text_content)
                    if 'error' in text_result:
                        logger.warning(f"文本情感分析失败: {text_result['error']}")
                        text_result = {"emotion": "中立", "confidence": 0.0}
                except Exception as e:
                    logger.warning(f"文本情感分析异常: {e}")
                    text_result = {"emotion": "中立", "confidence": 0.0}
            else:
                text_result = {"emotion": "中立", "confidence": 0.0}
                logger.debug(f"片段 {speaker} 无转录文本")
            
            # 5. 多模态融合
            try:
                audio_fusion_input = {
                    'top_emotion': audio_emotion.get('top_emotion', '中立'),
                    'confidence': audio_emotion.get('confidence', 0.0)
                }
                
                text_fusion_input = {
                    'emotion': text_result.get('emotion', '中立'),
                    'confidence': text_result.get('confidence', 0.0)
                }
                
                fusion_result = self.fusion_processor.fuse_emotions(
                    audio_fusion_input, text_fusion_input
                )
                
                if 'error' in fusion_result:
                    logger.warning(f"多模态融合失败: {fusion_result['error']}")
                    fused_emotion = audio_emotion.get('top_emotion', '中立')
                    fused_confidence = audio_emotion.get('confidence', 0.0)
                else:
                    fused_emotion = fusion_result.get('fused_emotion', '中立')
                    fused_confidence = fusion_result.get('fused_confidence', 0.0)
                    
            except Exception as e:
                logger.warning(f"多模态融合异常: {e}")
                fused_emotion = audio_emotion.get('top_emotion', '中立')
                fused_confidence = audio_emotion.get('confidence', 0.0)
            
            # 6. 构建结果
            result = {
                'speaker': speaker,
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,
                'transcription': text_content,
                'audio_emotion': audio_emotion.get('top_emotion', '中立'),
                'text_emotion': text_result.get('emotion', '中立'),
                'fused_emotion': fused_emotion,
                'audio_confidence': audio_emotion.get('confidence', 0.0),
                'text_confidence': text_result.get('confidence', 0.0),
                'fused_confidence': fused_confidence,
                
                # 额外的详细信息
                'audio_emotions_all': audio_emotion.get('all_emotions', []),
                'text_analysis_details': text_result,
                'has_transcription': bool(text_content),
                'segment_quality': self._assess_segment_quality(audio_emotion, text_result, duration)
            }
            
            logger.debug(f"片段分析完成: {speaker} - {fused_emotion} ({fused_confidence:.3f})")
            return result
            
        except Exception as e:
            logger.error(f"增强片段分析失败: {e}")
            return {
                'speaker': speaker,
                'start_time': start_time,
                'end_time': end_time,
                'duration': end_time - start_time,
                'error': str(e)
            }
    
    def _summarize_speaker_emotions(self, speaker_emotions: Dict) -> Dict:
        """汇总每个说话人的情感分析结果"""
        summary = {}
        
        for speaker, segments in speaker_emotions.items():
            emotions = []
            confidences = []
            durations = []
            total_duration = 0
            
            for segment in segments:
                if 'error' not in segment:
                    emotions.append(segment.get('fused_emotion', '中立'))
                    confidences.append(segment.get('fused_confidence', 0.0))
                    duration = segment.get('duration', 0.0)
                    durations.append(duration)
                    total_duration += duration
            
            if emotions:
                # 计算主要情感（多种方法）
                emotion_counts = {}
                emotion_weighted_scores = {}  # 置信度加权
                emotion_duration_scores = {}  # 时长加权
                
                for i, emotion in enumerate(emotions):
                    confidence = confidences[i]
                    duration = durations[i]
                    
                    # 基础计数
                    emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
                    
                    # 置信度加权分数
                    emotion_weighted_scores[emotion] = emotion_weighted_scores.get(emotion, 0) + confidence
                    
                    # 时长加权分数
                    emotion_duration_scores[emotion] = emotion_duration_scores.get(emotion, 0) + duration
                
                # 方法1：基于出现次数的主要情感
                dominant_by_count = max(emotion_counts, key=emotion_counts.get)
                
                # 方法2：基于置信度加权的主要情感
                dominant_by_confidence = max(emotion_weighted_scores, key=emotion_weighted_scores.get)
                
                # 方法3：基于时长加权的主要情感
                dominant_by_duration = max(emotion_duration_scores, key=emotion_duration_scores.get)
                
                # 综合判断：如果三种方法结果一致，使用该结果；否则使用置信度加权结果
                if dominant_by_count == dominant_by_confidence == dominant_by_duration:
                    final_dominant = dominant_by_count
                    dominance_method = "一致性判断"
                elif dominant_by_confidence == dominant_by_duration:
                    final_dominant = dominant_by_confidence
                    dominance_method = "置信度+时长加权"
                else:
                    final_dominant = dominant_by_confidence
                    dominance_method = "置信度加权"
                
                # 计算主要情感的占比
                total_segments = len(emotions)
                dominance_percentage = emotion_counts.get(final_dominant, 0) / total_segments
                
                # 计算平均置信度
                avg_confidence = np.mean(confidences)
                
                # 计算情感稳定性（主要情感的置信度标准差）
                dominant_confidences = [confidences[i] for i, e in enumerate(emotions) if e == final_dominant]
                emotion_stability = 1.0 - (np.std(dominant_confidences) if len(dominant_confidences) > 1 else 0.0)
                
                summary[speaker] = {
                    'dominant_emotion': final_dominant,
                    'dominance_method': dominance_method,
                    'dominance_percentage': dominance_percentage,
                    'average_confidence': avg_confidence,
                    'emotion_stability': emotion_stability,
                    'total_duration': total_duration,
                    'segment_count': len(segments),
                    'emotion_distribution': emotion_counts,
                    'emotion_weighted_scores': emotion_weighted_scores,
                    'emotion_duration_scores': emotion_duration_scores,
                    'all_emotions': emotions,
                    
                    # 额外的分析指标
                    'dominant_by_count': dominant_by_count,
                    'dominant_by_confidence': dominant_by_confidence,
                    'dominant_by_duration': dominant_by_duration,
                    'emotion_diversity': len(set(emotions)) / len(emotions),  # 情感多样性
                    'high_confidence_segments': len([c for c in confidences if c > 0.8]),  # 高置信度片段数
                }
            else:
                summary[speaker] = {
                    'dominant_emotion': '未知',
                    'dominance_method': '无数据',
                    'dominance_percentage': 0.0,
                    'average_confidence': 0.0,
                    'emotion_stability': 0.0,
                    'total_duration': total_duration,
                    'segment_count': len(segments),
                    'emotion_distribution': {},
                    'emotion_weighted_scores': {},
                    'emotion_duration_scores': {},
                    'all_emotions': [],
                    'dominant_by_count': '未知',
                    'dominant_by_confidence': '未知',
                    'dominant_by_duration': '未知',
                    'emotion_diversity': 0.0,
                    'high_confidence_segments': 0,
                }
        
        return summary

def format_diarization_report(result: Dict) -> str:
    """格式化说话人分离报告"""
    if 'error' in result:
        return f"❌ 说话人分离失败: {result['error']}"
    
    report = []
    report.append("🎭 说话人分离结果")
    report.append("=" * 50)
    
    diarization = result.get('diarization', {})
    report.append(f"📁 音频文件: {os.path.basename(result.get('audio_path', ''))}")
    report.append(f"👥 检测到说话人数量: {diarization.get('num_speakers', 0)}")
    report.append(f"📊 总片段数: {diarization.get('total_segments', 0)}")
    report.append(f"🔧 分离方法: {diarization.get('method', '未知')}")
    
    if 'note' in diarization:
        report.append(f"📝 注意: {diarization['note']}")
    
    report.append("\n📋 说话人片段详情:")
    report.append("-" * 40)
    
    for i, segment in enumerate(diarization.get('segments', []), 1):
        report.append(f"{i:2d}. {segment['speaker']} | "
                     f"{segment['start_time_str']} - {segment['end_time_str']} | "
                     f"时长: {segment['duration']:.2f}s")
    
    # 如果有情感分析结果
    if 'summary' in result:
        report.append("\n🎭 说话人情感分析汇总:")
        report.append("-" * 40)
        
        for speaker, summary in result['summary'].items():
            report.append(f"👤 {speaker}:")
            report.append(f"   主要情感: {summary['dominant_emotion']}")
            report.append(f"   平均置信度: {summary['average_confidence']:.3f}")
            report.append(f"   总时长: {summary['total_duration']:.2f}s")
            report.append(f"   片段数: {summary['segment_count']}")
    
    return "\n".join(report)

if __name__ == "__main__":
    # 测试代码
    print("🧪 测试说话人分离模块")
    print("=" * 50)
    
    # 测试说话人分离
    diarization_processor = SpeakerDiarizationProcessor()
    
    test_files = ["../对话访谈.wav"]
    # test_files = ["../惊讶.wav", "../惊讶_16k.wav", "../angry.wav"]
    
    for audio_file in test_files:
        if os.path.exists(audio_file):
            print(f"\n🎵 测试文件: {audio_file}")
            print("-" * 40)
            
            result = diarization_processor.diarize_audio(audio_file)
            print(format_diarization_report({'audio_path': audio_file, 'diarization': result}))
            break
    else:
        print("⚠️  未找到测试音频文件")