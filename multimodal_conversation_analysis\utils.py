"""
多模态对话分析系统工具模块
"""

import os
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import numpy as np

def setup_logging(config: Dict) -> logging.Logger:
    """设置日志系统"""
    logger = logging.getLogger("MultiModalAnalysis")
    logger.setLevel(getattr(logging, config.get("level", "INFO")))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 创建格式器
    formatter = logging.Formatter(config.get("format", "%(asctime)s - %(levelname)s - %(message)s"))
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果启用）
    if config.get("save_logs", False):
        log_file = config.get("log_file", "multimodal_analysis.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def validate_audio_file(file_path: str, supported_formats: List[str]) -> Tuple[bool, str]:
    """验证音频文件"""
    if not os.path.exists(file_path):
        return False, f"文件不存在: {file_path}"
    
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext not in supported_formats:
        return False, f"不支持的音频格式: {file_ext}"
    
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        return False, "文件为空"
    
    if file_size > 100 * 1024 * 1024:  # 100MB
        return False, "文件过大（超过100MB）"
    
    return True, "文件验证通过"

def normalize_emotion_label(emotion: str, emotion_mapping: Dict[str, str]) -> str:
    """标准化情感标签"""
    # 直接匹配
    if emotion in emotion_mapping:
        return emotion_mapping[emotion]
    
    # 模糊匹配
    emotion_lower = emotion.lower()
    for key, value in emotion_mapping.items():
        if emotion_lower in key.lower() or emotion_lower in value.lower():
            return value
    
    # 提取中文部分
    if "/" in emotion:
        chinese_part = emotion.split("/")[0]
        if chinese_part in emotion_mapping.values():
            return chinese_part
    
    return emotion

def calculate_confidence_score(scores: List[float], method: str = "max") -> float:
    """计算置信度分数"""
    if not scores:
        return 0.0
    
    if method == "max":
        return max(scores)
    elif method == "mean":
        return np.mean(scores)
    elif method == "weighted":
        # 加权平均，给最高分更大权重
        weights = np.exp(np.array(scores))
        return np.average(scores, weights=weights)
    else:
        return max(scores)

def calculate_emotion_consistency(audio_emotion: str, text_emotion: str, 
                                audio_confidence: float, text_confidence: float) -> Dict[str, Any]:
    """计算情感一致性"""
    # 情感是否一致
    is_consistent = audio_emotion == text_emotion
    
    # 计算一致性分数
    if is_consistent:
        consistency_score = 1.0
        confidence_boost = 0.1
    else:
        # 基于置信度差异计算一致性
        confidence_diff = abs(audio_confidence - text_confidence)
        consistency_score = max(0.0, 1.0 - confidence_diff)
        confidence_boost = -0.1
    
    return {
        "is_consistent": is_consistent,
        "consistency_score": consistency_score,
        "confidence_boost": confidence_boost,
        "explanation": "音频和文本情感一致" if is_consistent else "音频和文本情感不一致"
    }

def weighted_fusion(audio_result: Dict, text_result: Dict, 
                   audio_weight: float = 0.6, text_weight: float = 0.4) -> Dict[str, Any]:
    """加权融合音频和文本结果"""
    # 提取情感和置信度
    audio_emotion = audio_result.get("emotion", "中立")
    audio_confidence = audio_result.get("confidence", 0.0)
    text_emotion = text_result.get("emotion", "中立")
    text_confidence = text_result.get("confidence", 0.0)
    
    # 计算一致性
    consistency = calculate_emotion_consistency(
        audio_emotion, text_emotion, audio_confidence, text_confidence
    )
    
    # 根据一致性调整权重
    if consistency["is_consistent"]:
        # 一致时，提升整体置信度
        fused_emotion = audio_emotion
        fused_confidence = min(1.0, 
            audio_weight * audio_confidence + 
            text_weight * text_confidence + 
            consistency["confidence_boost"]
        )
    else:
        # 不一致时，选择置信度更高的
        if audio_confidence > text_confidence:
            fused_emotion = audio_emotion
            fused_confidence = audio_confidence * 0.8  # 降低置信度
        else:
            fused_emotion = text_emotion
            fused_confidence = text_confidence * 0.8
    
    return {
        "fused_emotion": fused_emotion,
        "fused_confidence": round(fused_confidence, 4),
        "consistency_score": round(consistency["consistency_score"], 4),
        "fusion_method": "weighted_average",
        "weights": {"audio": audio_weight, "text": text_weight}
    }

def save_analysis_result(result: Dict, output_dir: str, 
                        filename_prefix: str = "analysis", 
                        format_type: str = "json") -> str:
    """保存分析结果"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if format_type == "json":
        filename = f"{filename_prefix}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    
    elif format_type == "txt":
        filename = f"{filename_prefix}_{timestamp}.txt"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(format_analysis_report(result))
    
    else:
        raise ValueError(f"不支持的格式: {format_type}")
    
    return filepath

def format_analysis_report(result: Dict) -> str:
    """格式化分析报告"""
    report = []
    report.append("=" * 60)
    report.append("多模态情感分析报告")
    report.append("=" * 60)
    
    # 基本信息
    report.append(f"分析时间: {result.get('timestamp', 'N/A')}")
    report.append(f"音频文件: {result.get('audio_file', 'N/A')}")
    report.append("")
    
    # 转录结果
    if 'transcription' in result:
        trans = result['transcription']
        report.append("📝 语音转录结果:")
        report.append(f"  文本内容: {trans.get('text', 'N/A')}")
        report.append(f"  转录置信度: {trans.get('confidence', 0):.2%}")
        report.append("")
    
    # 音频情感分析
    if 'audio_emotion' in result:
        audio = result['audio_emotion']
        report.append("🎵 音频情感分析:")
        report.append(f"  识别情感: {audio.get('emotion', 'N/A')}")
        report.append(f"  置信度: {audio.get('confidence', 0):.2%}")
        if 'top_emotions' in audio:
            report.append("  Top 3 情感:")
            for i, (emotion, score) in enumerate(audio['top_emotions'][:3], 1):
                report.append(f"    {i}. {emotion}: {score:.2%}")
        report.append("")
    
    # 文本情感分析
    if 'text_emotion' in result:
        text = result['text_emotion']
        report.append("📄 文本情感分析:")
        report.append(f"  识别情感: {text.get('emotion', 'N/A')}")
        report.append(f"  置信度: {text.get('confidence', 0):.2%}")
        if 'keywords' in text:
            report.append(f"  关键词: {', '.join(text['keywords'])}")
        report.append("")
    
    # 多模态融合结果
    if 'multimodal_fusion' in result:
        fusion = result['multimodal_fusion']
        report.append("🔄 多模态融合结果:")
        report.append(f"  最终情感: {fusion.get('fused_emotion', 'N/A')}")
        report.append(f"  融合置信度: {fusion.get('fused_confidence', 0):.2%}")
        report.append(f"  一致性分数: {fusion.get('consistency_score', 0):.2%}")
        if 'weights' in fusion:
            weights = fusion['weights']
            report.append(f"  融合权重: 音频{weights.get('audio', 0):.1%}, 文本{weights.get('text', 0):.1%}")
        report.append("")
    
    # 分析总结
    report.append("📊 分析总结:")
    if 'multimodal_fusion' in result:
        fusion = result['multimodal_fusion']
        consistency = fusion.get('consistency_score', 0)
        if consistency > 0.8:
            report.append("  ✅ 音频和文本情感高度一致，结果可信度高")
        elif consistency > 0.5:
            report.append("  ⚠️  音频和文本情感部分一致，建议进一步验证")
        else:
            report.append("  ❌ 音频和文本情感不一致，可能存在复杂情感或识别错误")
    
    report.append("=" * 60)
    
    return "\n".join(report)

def get_emotion_color(emotion: str) -> str:
    """获取情感对应的颜色代码（用于可视化）"""
    color_map = {
        "开心": "#FFD700",  # 金色
        "难过": "#4169E1",  # 蓝色
        "生气": "#FF4500",  # 红橙色
        "恐惧": "#800080",  # 紫色
        "吃惊": "#FF69B4",  # 粉色
        "厌恶": "#8B4513",  # 棕色
        "中立": "#808080",  # 灰色
    }
    return color_map.get(emotion, "#000000")

def calculate_processing_time(start_time: float) -> Dict[str, float]:
    """计算处理时间"""
    end_time = time.time()
    total_time = end_time - start_time
    
    return {
        "total_time": round(total_time, 3),
        "start_time": start_time,
        "end_time": end_time
    }