#!/usr/bin/env python3
"""
说话人分离功能测试脚本
"""

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 添加父目录到路径，以便导入emotion2vec相关模块
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_speaker_diarization():
    """测试说话人分离功能"""
    print("🧪 开始测试说话人分离功能")
    print("="*60)
    
    try:
        # 导入模块
        from speaker_diarization import (
            SpeakerDiarizationProcessor, 
            MultiSpeakerEmotionAnalyzer,
            format_diarization_report
        )
        
        print("✅ 说话人分离模块导入成功")
        
        # 查找测试音频文件
        test_files = []
        possible_files = [
            "../对话访谈.wav"
        ]
        # possible_files = [
        #     "../对话访谈.wav", "../惊讶.wav", "../惊讶_16k.wav", "../angry.wav",
        #     "../../惊讶.wav", "../../惊讶_16k.wav", "../../angry.wav"
        # ]

        for file_path in possible_files:
            if os.path.exists(file_path):
                test_files.append(file_path)
        
        if not test_files:
            print("⚠️  未找到测试音频文件")
            print("请确保以下文件存在：")
            for file_path in possible_files:
                print(f"  - {file_path}")
            return
        
        print(f"📁 找到 {len(test_files)} 个测试文件")
        
        # 测试基础说话人分离
        print("\n🔍 测试基础说话人分离...")
        print("-" * 50)
        
        diarization_processor = SpeakerDiarizationProcessor()
        
        for audio_file in test_files[:1]:  # 只测试第一个文件
            print(f"\n🎵 测试文件: {audio_file}")
            print("-" * 40)
            
            # 基础说话人分离
            diarization_result = diarization_processor.diarize_audio(audio_file)
            
            if 'error' in diarization_result:
                print(f"❌ 说话人分离失败: {diarization_result['error']}")
                continue
            
            # 显示分离结果
            report_data = {
                'audio_path': audio_file,
                'diarization': diarization_result
            }
            print(format_diarization_report(report_data))
        
        # 测试多说话人情感分析
        print("\n\n🎭 测试多说话人情感分析...")
        print("-" * 50)
        
        multi_speaker_analyzer = MultiSpeakerEmotionAnalyzer()
        
        for audio_file in test_files[:1]:  # 只测试第一个文件
            print(f"\n🎵 测试文件: {audio_file}")
            print("-" * 40)
            
            # 多说话人情感分析
            analysis_result = multi_speaker_analyzer.analyze_multi_speaker_audio(audio_file)
            
            if 'error' in analysis_result:
                print(f"❌ 多说话人情感分析失败: {analysis_result['error']}")
                continue
            
            # 显示完整分析结果
            print(format_diarization_report(analysis_result))
            
            # 显示详细的片段分析结果
            print("\n📋 详细片段分析:")
            print("-" * 40)
            
            for i, segment in enumerate(analysis_result.get('segment_results', []), 1):
                if 'error' not in segment:
                    print(f"{i:2d}. {segment['speaker']} | "
                         f"{segment['start_time']:.2f}s - {segment['end_time']:.2f}s")
                    print(f"    转录: {segment.get('transcription', 'N/A')}")
                    print(f"    音频情感: {segment.get('audio_emotion', 'N/A')} "
                         f"({segment.get('audio_confidence', 0.0):.3f})")
                    print(f"    文本情感: {segment.get('text_emotion', 'N/A')} "
                         f"({segment.get('text_confidence', 0.0):.3f})")
                    print(f"    融合情感: {segment.get('fused_emotion', 'N/A')} "
                         f"({segment.get('fused_confidence', 0.0):.3f})")
                    print()
                else:
                    print(f"{i:2d}. {segment['speaker']} | 分析失败: {segment['error']}")
        
        print("\n🎉 说话人分离功能测试完成！")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 安装pyannote.audio: pip install pyannote.audio")
        print("2. 如果没有Hugging Face token，系统会自动使用简单的能量检测方法")
        print("3. 确保所有依赖都已正确安装")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_installation():
    """测试安装状态"""
    print("🔧 检查说话人分离依赖安装状态")
    print("="*50)
    
    # 检查pyannote.audio
    try:
        import pyannote.audio
        print("✅ pyannote.audio 已安装")
        print(f"   版本: {pyannote.audio.__version__}")
    except ImportError:
        print("❌ pyannote.audio 未安装")
        print("   安装命令: pip install pyannote.audio")
    
    # 检查其他依赖
    dependencies = [
        ('torch', 'torch'),
        ('librosa', 'librosa'),
        ('soundfile', 'soundfile'),
        ('numpy', 'numpy')
    ]
    
    for name, module in dependencies:
        try:
            mod = __import__(module)
            version = getattr(mod, '__version__', '未知版本')
            print(f"✅ {name} 已安装 (版本: {version})")
        except ImportError:
            print(f"❌ {name} 未安装")

if __name__ == "__main__":
    print("🚀 说话人分离功能测试")
    print("="*60)
    
    # 首先检查安装状态
    test_installation()
    
    print("\n")
    
    # 然后进行功能测试
    test_speaker_diarization()