#!/usr/bin/env python3
"""
音频处理模块
包含语音转录(ASR)和音频情感分析功能
"""

import os
import soundfile as sf
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioProcessor:
    """音频处理基类"""
    
    def __init__(self):
        self.sample_rate = 16000  # 标准采样率
        
    def load_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """加载音频文件"""
        try:
            audio, sr = sf.read(audio_path)
            
            # 转换为单声道
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)
                
            # 重采样到16kHz
            if sr != self.sample_rate:
                import librosa
                audio = librosa.resample(audio, orig_sr=sr, target_sr=self.sample_rate)
                
            return audio, self.sample_rate
        except Exception as e:
            logger.error(f"加载音频文件失败: {e}")
            raise

class ASRProcessor(AudioProcessor):
    """语音转录处理器"""
    
    def __init__(self):
        super().__init__()
        self.asr_model = None
        self._load_model()
        
    def _load_model(self):
        """加载ASR模型"""
        try:
            from funasr import AutoModel
            logger.info("正在加载FunASR模型...")
            
            self.asr_model = AutoModel(
                model="iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
                hub="ms"
            )
            logger.info("✅ ASR模型加载完成")
            
        except Exception as e:
            logger.error(f"ASR模型加载失败: {e}")
            raise
    
    def transcribe(self, audio_path: str) -> Dict:
        """
        语音转录
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            转录结果字典，包含文本和时间戳信息
        """
        try:
            logger.info(f"正在转录音频: {audio_path}")
            
            # 使用FunASR进行转录
            result = self.asr_model.generate(
                input=audio_path,
                batch_size_s=300,
                hotword="",
                use_itn=True
            )
            
            if result and len(result) > 0:
                transcription = result[0]
                
                # 提取文本内容
                if isinstance(transcription, dict) and 'text' in transcription:
                    text = transcription['text']
                elif isinstance(transcription, str):
                    text = transcription
                else:
                    text = str(transcription)
                
                logger.info(f"转录完成: {text}")
                
                return {
                    'text': text,
                    'confidence': 1.0,  # FunASR暂不提供置信度
                    'audio_path': audio_path,
                    'raw_result': result
                }
            else:
                logger.warning("转录结果为空")
                return {
                    'text': "",
                    'confidence': 0.0,
                    'audio_path': audio_path,
                    'raw_result': result
                }
                
        except Exception as e:
            logger.error(f"转录失败: {e}")
            return {
                'text': "",
                'confidence': 0.0,
                'audio_path': audio_path,
                'error': str(e)
            }

class AudioEmotionProcessor(AudioProcessor):
    """音频情感分析处理器"""
    
    def __init__(self):
        super().__init__()
        self.emotion_model = None
        self._load_model()
        
    def _load_model(self):
        """加载emotion2vec模型"""
        try:
            from funasr import AutoModel
            logger.info("正在加载emotion2vec模型...")
            
            self.emotion_model = AutoModel(
                model="iic/emotion2vec_plus_base",
                hub="ms"
            )
            logger.info("✅ emotion2vec模型加载完成")
            
        except Exception as e:
            logger.error(f"emotion2vec模型加载失败: {e}")
            raise
    
    def analyze_emotion(self, audio_path: str) -> Dict:
        """
        分析音频情感
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            情感分析结果字典
        """
        try:
            logger.info(f"正在分析音频情感: {audio_path}")
            
            # 使用emotion2vec进行情感分析
            result = self.emotion_model.generate(
                audio_path,
                output_dir="./outputs",
                granularity="utterance",
                extract_embedding=False
            )
            
            logger.debug(f"emotion2vec原始结果: {result}")
            
            if result and len(result) > 0:
                emotion_result = result[0]
                logger.debug(f"第一个结果: {emotion_result}")
                
                # 检查结果格式
                if isinstance(emotion_result, dict) and 'labels' in emotion_result and 'scores' in emotion_result:
                    labels = emotion_result['labels']
                    scores = emotion_result['scores']
                    
                    logger.debug(f"标签: {labels}")
                    logger.debug(f"得分: {scores}")
                    
                    # 确保labels和scores是列表且长度一致
                    if isinstance(labels, list) and isinstance(scores, list) and len(labels) == len(scores):
                        # 创建情感-得分对并排序
                        emotion_scores = list(zip(labels, scores))
                        emotion_scores.sort(key=lambda x: x[1], reverse=True)
                        
                        logger.debug(f"排序后的情感得分: {emotion_scores}")
                        
                        # 提取最高得分的情感
                        top_emotion_raw, top_score = emotion_scores[0]
                        
                        # 解析情感标签 - 处理多种格式
                        chinese_emotion = self._parse_emotion_label(top_emotion_raw)
                        
                        # 获取英文标签
                        english_emotion = self._get_english_emotion(top_emotion_raw)
                        
                        logger.info(f"情感分析完成: {chinese_emotion} ({top_score:.4f})")
                        
                        # 处理所有情感得分，转换为中文标签
                        processed_emotions = []
                        for label, score in emotion_scores:
                            chinese_label = self._parse_emotion_label(label)
                            processed_emotions.append((chinese_label, score))
                        
                        return {
                            'top_emotion': chinese_emotion,
                            'top_emotion_en': english_emotion,
                            'confidence': float(top_score),
                            'all_emotions': processed_emotions,
                            'audio_path': audio_path,
                            'raw_result': result
                        }
                    else:
                        logger.error(f"标签和得分格式不匹配: labels={type(labels)}, scores={type(scores)}")
                        return self._create_error_result(audio_path, "标签和得分格式不匹配", result)
                else:
                    logger.error(f"结果格式不正确: {emotion_result}")
                    return self._create_error_result(audio_path, "结果格式不正确", result)
            else:
                logger.warning("情感分析结果为空")
                return self._create_error_result(audio_path, "结果为空", result)
                
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            import traceback
            traceback.print_exc()
            return self._create_error_result(audio_path, str(e), None)
    
    def _parse_emotion_label(self, label: str) -> str:
        """
        解析情感标签，提取中文部分
        
        Args:
            label: 原始标签（可能包含中文/英文格式）
            
        Returns:
            中文情感标签
        """
        try:
            # 处理 "中文/英文" 格式
            if '/' in label:
                chinese_part = label.split('/')[0].strip()
                return chinese_part
            
            # 处理纯英文标签，映射到中文
            english_to_chinese = {
                'angry': '生气',
                'disgusted': '厌恶', 
                'fearful': '恐惧',
                'happy': '开心',
                'neutral': '中立',
                'sad': '难过',
                'surprised': '吃惊',
                'unknown': '未知',
                'other': '其他',
                '<unk>': '未知',  # 处理emotion2vec模型的<unk>标签
                'unk': '未知'     # 处理可能的unk变体
            }
            
            label_lower = label.lower().strip()
            if label_lower in english_to_chinese:
                return english_to_chinese[label_lower]
            
            # 如果已经是中文，直接返回
            chinese_emotions = ['生气', '厌恶', '恐惧', '开心', '中立', '难过', '吃惊', '未知', '其他']
            if label.strip() in chinese_emotions:
                return label.strip()
            
            # 默认返回原标签
            logger.warning(f"无法解析情感标签: {label}")
            return label.strip()
            
        except Exception as e:
            logger.error(f"标签解析失败: {e}")
            return "未知"
    
    def _get_english_emotion(self, label: str) -> str:
        """
        获取英文情感标签
        
        Args:
            label: 原始标签
            
        Returns:
            英文情感标签
        """
        try:
            # 处理 "中文/英文" 格式
            if '/' in label:
                english_part = label.split('/')[1].strip()
                return english_part
            
            # 处理纯中文标签，映射到英文
            chinese_to_english = {
                '生气': 'angry',
                '厌恶': 'disgusted',
                '恐惧': 'fearful', 
                '开心': 'happy',
                '中立': 'neutral',
                '难过': 'sad',
                '吃惊': 'surprised',
                '未知': 'unknown',
                '其他': 'other'
            }
            
            if label.strip() in chinese_to_english:
                return chinese_to_english[label.strip()]
            
            # 如果已经是英文，直接返回
            return label.lower().strip()
            
        except Exception as e:
            logger.error(f"英文标签获取失败: {e}")
            return "unknown"
    
    def _create_error_result(self, audio_path: str, error_msg: str, raw_result) -> Dict:
        """创建错误结果"""
        return {
            'top_emotion': "错误",
            'top_emotion_en': "error",
            'confidence': 0.0,
            'all_emotions': [],
            'audio_path': audio_path,
            'error': error_msg,
            'raw_result': raw_result
        }

class IntegratedAudioProcessor:
    """集成音频处理器 - 同时进行转录和情感分析"""
    
    def __init__(self):
        self.asr_processor = ASRProcessor()
        self.emotion_processor = AudioEmotionProcessor()
        
    def process_audio(self, audio_path: str) -> Dict:
        """
        完整处理音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            包含转录和情感分析的完整结果
        """
        logger.info(f"开始完整处理音频: {audio_path}")
        
        # 并行处理转录和情感分析
        transcription_result = self.asr_processor.transcribe(audio_path)
        emotion_result = self.emotion_processor.analyze_emotion(audio_path)
        
        # 合并结果
        integrated_result = {
            'audio_path': audio_path,
            'transcription': transcription_result,
            'emotion': emotion_result,
            'timestamp': None,  # 后续添加时间戳功能
        }
        
        logger.info("音频处理完成")
        return integrated_result

if __name__ == "__main__":
    # 测试代码
    processor = IntegratedAudioProcessor()
    
    # 测试音频文件
    test_audio = "惊讶.wav"
    if os.path.exists(test_audio):
        result = processor.process_audio(test_audio)
        
        print("="*50)
        print("🎵 音频处理结果")
        print("="*50)
        print(f"📁 文件: {result['audio_path']}")
        print(f"📝 转录: {result['transcription']['text']}")
        print(f"🎭 情感: {result['emotion']['top_emotion']} ({result['emotion']['confidence']:.4f})")
        print("="*50)
    else:
        print(f"测试音频文件 {test_audio} 不存在")