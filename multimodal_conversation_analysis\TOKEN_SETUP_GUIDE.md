# Hugging Face Token 设置指南

## 快速设置（推荐）

### 方法1：使用自动设置工具

运行以下命令，工具会自动帮您设置token：

```bash
python set_token.py
```

这个工具会：
1. 自动打开Hugging Face token页面
2. 指导您获取token
3. 直接修改代码中的token配置
4. 测试token是否有效

### 方法2：手动设置

1. **获取Hugging Face Token**
   - 访问：https://hf.co/settings/tokens
   - 点击 "New token"
   - 输入名称（如：speaker-diarization）
   - 选择 "Read" 权限
   - 复制生成的token

2. **修改代码中的token**
   
   打开 `speaker_diarization.py` 文件，找到以下行：
   
   ```python
   HUGGINGFACE_TOKEN = "your_token_here"
   ```
   
   将 `"your_token_here"` 替换为您的实际token：
   
   ```python
   HUGGINGFACE_TOKEN = "hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
   ```

3. **接受模型使用条款**
   
   访问以下链接并点击 "Agree and access repository"：
   - https://hf.co/pyannote/speaker-diarization-3.1
   - https://hf.co/pyannote/segmentation-3.0
   - https://hf.co/pyannote/wespeaker-voxceleb-resnet34-LM

## 测试设置

设置完成后，运行以下命令测试：

```bash
# 测试说话人分离功能
python test_speaker_diarization.py

# 或者运行完整测试
python test_comprehensive.py
```

## 常见问题

### Q: 为什么模型还是加载失败？
A: 可能的原因：
1. Token无效或权限不足
2. 未接受模型使用条款
3. 网络连接问题
4. 需要等待几分钟让设置生效

### Q: 如何知道token是否设置成功？
A: 运行测试时，如果看到以下信息说明成功：
```
✅ PyAnnote模型加载成功！
```

如果看到以下信息说明失败：
```
⚠️ 模型加载失败，将使用简单的分离方法
```

### Q: 不想在代码中硬编码token怎么办？
A: 您可以：
1. 将 `HUGGINGFACE_TOKEN = "your_token_here"` 改为 `HUGGINGFACE_TOKEN = None`
2. 设置环境变量：`set HF_TOKEN=your_token_here`（Windows）

## 安全提醒

⚠️ **重要**：如果您要分享代码，请确保：
1. 不要将包含真实token的代码提交到公共仓库
2. 可以将token设置为 `None` 并使用环境变量
3. 或者在 `.gitignore` 中忽略包含token的文件

## 支持

如果遇到问题，请：
1. 检查token格式是否正确（通常以 `hf_` 开头）
2. 确认已接受所有模型的使用条款
3. 尝试重新启动Python环境
4. 检查网络连接