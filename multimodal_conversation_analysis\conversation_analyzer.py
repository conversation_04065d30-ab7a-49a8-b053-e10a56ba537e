#!/usr/bin/env python3
"""
多模态对话分析系统主入口
整合音频处理、文本处理和多模态融合
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

from .audio_processing import IntegratedAudioProcessor
from .text_processing import TextEmotionProcessor
from .multimodal_fusion import SimpleMultiModalFusion
from .speaker_diarization import MultiSpeakerEmotionAnalyzer, format_diarization_report
from .config import Config
from .utils import (
    setup_logging, validate_audio_file, normalize_emotion_label,
    weighted_fusion, save_analysis_result, format_analysis_report,
    calculate_processing_time
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConversationAnalysisSystem:
    """多模态对话分析系统"""
    
    def __init__(self, use_auth_token: Optional[str] = None):
        """初始化系统"""
        # 设置日志
        self.logger = setup_logging(Config.LOGGING_CONFIG)
        self.logger.info("初始化多模态对话分析系统")
        
        # 创建输出目录
        self.output_dir = Config.create_output_dir()
        
        # 初始化各个处理器
        try:
            self.logger.info("正在加载音频处理器...")
            self.audio_processor = IntegratedAudioProcessor()
            
            self.logger.info("正在加载文本处理器...")
            self.text_processor = TextEmotionProcessor()
            
            self.logger.info("正在加载多模态融合器...")
            self.fusion_processor = SimpleMultiModalFusion()
            
            self.logger.info("正在加载说话人分离处理器...")
            # 传递token给说话人分析器
            self.speaker_analyzer = MultiSpeakerEmotionAnalyzer(use_auth_token=use_auth_token)
            
            self.logger.info("系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            raise
    
    def analyze_single_audio(self, audio_file: str) -> Dict[str, Any]:
        """分析单个音频文件"""
        start_time = time.time()
        self.logger.info(f"开始分析音频文件: {audio_file}")
        
        # 验证音频文件
        is_valid, message = validate_audio_file(audio_file, Config.AUDIO_CONFIG["supported_formats"])
        if not is_valid:
            error_msg = f"音频文件验证失败: {message}"
            self.logger.error(error_msg)
            return {"error": error_msg}
        
        try:
            # 1. 音频处理（转录 + 情感分析）
            self.logger.info("正在进行音频处理...")
            audio_result = self.audio_processor.process_audio(audio_file)
            
            if 'error' in audio_result:
                return audio_result
            
            # 2. 文本情感分析
            transcription = audio_result.get('transcription', {})
            text_content = transcription.get('text', '')
            
            if text_content.strip():
                self.logger.info("正在进行文本情感分析...")
                text_result = self.text_processor.analyze_emotion(text_content)
            else:
                self.logger.warning("转录文本为空，跳过文本情感分析")
                text_result = {
                    "emotion": "中立",
                    "confidence": 0.0,
                    "method": "default"
                }
            
            # 3. 多模态融合
            self.logger.info("正在进行多模态融合...")
            audio_emotion = audio_result.get('emotion_analysis', {})
            
            # 准备融合输入数据
            audio_fusion_input = {
                "top_emotion": audio_emotion.get('emotion', '中立'),
                "confidence": audio_emotion.get('confidence', 0.0)
            }
            
            text_fusion_input = {
                "emotion": text_result.get('emotion', '中立'),
                "confidence": text_result.get('confidence', 0.0)
            }
            
            # 使用新的自适应融合算法
            fusion_result = self.fusion_processor.fuse_emotions(
                audio_fusion_input, 
                text_fusion_input
            )
            
            # 标准化情感标签（用于最终结果）
            audio_emotion_normalized = normalize_emotion_label(
                audio_emotion.get('emotion', '中立'), 
                Config.get_emotion_mapping()
            )
            text_emotion_normalized = normalize_emotion_label(
                text_result.get('emotion', '中立'),
                Config.get_emotion_mapping()
            )
            
            # 4. 整合结果
            processing_time = calculate_processing_time(start_time)
            
            result = {
                "timestamp": datetime.now().isoformat(),
                "audio_file": os.path.basename(audio_file),
                "processing_time": processing_time,
                "transcription": {
                    "text": text_content,
                    "confidence": transcription.get('confidence', 0.0)
                },
                "audio_emotion": {
                    "emotion": audio_emotion_normalized,
                    "confidence": audio_emotion.get('confidence', 0.0),
                    "top_emotions": audio_emotion.get('top_emotions', [])
                },
                "text_emotion": {
                    "emotion": text_emotion_normalized,
                    "confidence": text_result.get('confidence', 0.0),
                    "method": text_result.get('method', 'unknown'),
                    "keywords": text_result.get('keywords', [])
                },
                "multimodal_fusion": fusion_result
            }
            
            # 5. 保存结果
            if Config.OUTPUT_CONFIG["save_results"]:
                try:
                    saved_file = save_analysis_result(
                        result, 
                        self.output_dir,
                        f"analysis_{os.path.splitext(os.path.basename(audio_file))[0]}",
                        Config.OUTPUT_CONFIG["save_format"]
                    )
                    self.logger.info(f"分析结果已保存到: {saved_file}")
                    result["saved_file"] = saved_file
                except Exception as e:
                    self.logger.warning(f"保存结果失败: {e}")
            
            self.logger.info(f"音频分析完成，耗时: {processing_time['total_time']:.2f}秒")
            return result
            
        except Exception as e:
            error_msg = f"分析过程中发生错误: {e}"
            self.logger.error(error_msg)
            import traceback
            self.logger.debug(traceback.format_exc())
            return {"error": error_msg}
    
    def analyze_multi_speaker_audio(self, audio_file: str) -> Dict[str, Any]:
        """分析多说话人音频文件"""
        start_time = time.time()
        self.logger.info(f"开始多说话人分析音频文件: {audio_file}")
        
        # 验证音频文件
        is_valid, message = validate_audio_file(audio_file, Config.AUDIO_CONFIG["supported_formats"])
        if not is_valid:
            error_msg = f"音频文件验证失败: {message}"
            self.logger.error(error_msg)
            return {"error": error_msg}
        
        try:
            # 使用说话人分离处理器进行多说话人情感分析
            self.logger.info("正在进行多说话人分离和情感分析...")
            result = self.speaker_analyzer.analyze_multi_speaker_audio(audio_file)
            
            if 'error' in result:
                return result
            
            # 添加处理时间信息
            processing_time = calculate_processing_time(start_time)
            result["processing_time"] = processing_time
            
            # 保存结果
            if Config.OUTPUT_CONFIG["save_results"]:
                try:
                    saved_file = save_analysis_result(
                        result, 
                        self.output_dir,
                        f"multi_speaker_analysis_{os.path.splitext(os.path.basename(audio_file))[0]}",
                        Config.OUTPUT_CONFIG["save_format"]
                    )
                    self.logger.info(f"多说话人分析结果已保存到: {saved_file}")
                    result["saved_file"] = saved_file
                except Exception as e:
                    self.logger.warning(f"保存结果失败: {e}")
            
            self.logger.info(f"多说话人分析完成，耗时: {processing_time['total_time']:.2f}秒")
            return result
            
        except Exception as e:
            error_msg = f"多说话人分析过程中发生错误: {e}"
            self.logger.error(error_msg)
            import traceback
            self.logger.debug(traceback.format_exc())
            return {"error": error_msg}
    
    def print_analysis_report(self, result: Dict[str, Any]) -> None:
        """打印分析报告"""
        if 'error' in result:
            print(f"❌ 分析失败: {result['error']}")
            return
        
        # 检查是否为多说话人分析结果
        if 'diarization' in result and 'segment_results' in result:
            # 多说话人分析结果
            print(format_diarization_report(result))
        else:
            # 单说话人分析结果
            print(format_analysis_report(result))

def main():
    """主函数 - 用于测试"""
    print("🚀 多模态对话分析系统测试")
    print("=" * 50)
    
    # 查找测试音频文件
    test_files = []
    possible_files = [
        "惊讶.wav", "惊讶_16k.wav", "angry.wav", 
        "../惊讶.wav", "../惊讶_16k.wav", "../angry.wav"
    ]
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            test_files.append(file_path)
    
    if not test_files:
        print("⚠️  未找到测试音频文件")
        print("请确保以下文件存在：")
        for file_path in possible_files:
            print(f"  - {file_path}")
        return
    
    # 初始化系统
    try:
        system = ConversationAnalysisSystem()
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return
    
    # 选择测试模式
    print("\n📋 请选择测试模式:")
    print("1. 单说话人分析")
    print("2. 多说话人分析")
    print("3. 两种模式都测试")
    
    try:
        choice = input("\n请输入选择 (1/2/3): ").strip()
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
        return
    
    if choice not in ['1', '2', '3']:
        choice = '1'  # 默认单说话人分析
    
    # 测试每个音频文件
    for audio_file in test_files:
        print(f"\n🎵 测试文件: {audio_file}")
        print("=" * 60)
        
        if choice in ['1', '3']:
            print("\n📊 单说话人分析:")
            print("-" * 40)
            
            result = system.analyze_single_audio(audio_file)
            
            if 'error' in result:
                print(f"❌ 单说话人分析失败: {result['error']}")
            else:
                print("✅ 单说话人分析成功")
                system.print_analysis_report(result)
        
        if choice in ['2', '3']:
            print("\n👥 多说话人分析:")
            print("-" * 40)
            
            multi_result = system.analyze_multi_speaker_audio(audio_file)
            
            if 'error' in multi_result:
                print(f"❌ 多说话人分析失败: {multi_result['error']}")
            else:
                print("✅ 多说话人分析成功")
                system.print_analysis_report(multi_result)
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()