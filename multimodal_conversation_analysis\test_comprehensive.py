#!/usr/bin/env python3
"""
多模态对话分析系统综合测试脚本
包含单说话人分析和多说话人分析功能
"""

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 添加父目录到路径，以便导入emotion2vec相关模块
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_comprehensive_system():
    """综合测试系统功能"""
    print("🚀 多模态对话分析系统综合测试")
    print("="*60)
    
    try:
        # 导入主系统
        from conversation_analyzer import ConversationAnalysisSystem
        
        print("✅ 系统模块导入成功")
        
        # 查找测试音频文件
        test_files = []
        possible_files = [
            "../惊讶.wav", "../惊讶_16k.wav", "../angry.wav",
            "../../惊讶.wav", "../../惊讶_16k.wav", "../../angry.wav"
        ]
        
        for file_path in possible_files:
            if os.path.exists(file_path):
                test_files.append(file_path)
        
        if not test_files:
            print("⚠️  未找到测试音频文件")
            print("请确保以下文件存在：")
            for file_path in possible_files:
                print(f"  - {file_path}")
            return
        
        print(f"📁 找到 {len(test_files)} 个测试文件")
        
        # 初始化系统
        print("\n🔧 初始化系统...")
        print("-" * 50)
        
        try:
            system = ConversationAnalysisSystem()
            print("✅ 系统初始化成功")
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            return
        
        # 测试每个音频文件
        for i, audio_file in enumerate(test_files, 1):
            print(f"\n🎵 测试文件 {i}/{len(test_files)}: {os.path.basename(audio_file)}")
            print("="*60)
            
            # 1. 单说话人分析测试
            print("\n📊 单说话人分析测试:")
            print("-" * 40)
            
            try:
                single_result = system.analyze_single_audio(audio_file)
                
                if 'error' in single_result:
                    print(f"❌ 单说话人分析失败: {single_result['error']}")
                else:
                    print("✅ 单说话人分析成功")
                    
                    # 显示关键信息
                    transcription = single_result.get('transcription', {})
                    audio_emotion = single_result.get('audio_emotion', {})
                    text_emotion = single_result.get('text_emotion', {})
                    fusion = single_result.get('multimodal_fusion', {})
                    
                    print(f"📝 转录文本: {transcription.get('text', 'N/A')}")
                    print(f"🎵 音频情感: {audio_emotion.get('emotion', 'N/A')} "
                         f"({audio_emotion.get('confidence', 0.0):.3f})")
                    print(f"📄 文本情感: {text_emotion.get('emotion', 'N/A')} "
                         f"({text_emotion.get('confidence', 0.0):.3f})")
                    print(f"🔀 融合情感: {fusion.get('emotion', 'N/A')} "
                         f"({fusion.get('confidence', 0.0):.3f})")
                    print(f"⏱️  处理时间: {single_result.get('processing_time', {}).get('total_time', 0.0):.2f}秒")
                    
            except Exception as e:
                print(f"❌ 单说话人分析异常: {e}")
            
            # 2. 多说话人分析测试
            print("\n👥 多说话人分析测试:")
            print("-" * 40)
            
            try:
                multi_result = system.analyze_multi_speaker_audio(audio_file)
                
                if 'error' in multi_result:
                    print(f"❌ 多说话人分析失败: {multi_result['error']}")
                else:
                    print("✅ 多说话人分析成功")
                    
                    # 显示说话人分离信息
                    diarization = multi_result.get('diarization', {})
                    segments = multi_result.get('segment_results', [])
                    summary = multi_result.get('summary', {})
                    
                    print(f"👤 检测到说话人数: {diarization.get('num_speakers', 0)}")
                    print(f"📊 音频片段数: {len(segments)}")
                    print(f"⏱️  总时长: {diarization.get('total_duration', 0.0):.2f}秒")
                    print(f"🔧 分离方法: {diarization.get('method', 'N/A')}")
                    
                    # 显示每个片段的简要信息
                    if segments:
                        print("\n📋 片段概览:")
                        for j, segment in enumerate(segments[:3], 1):  # 只显示前3个片段
                            if 'error' not in segment:
                                print(f"  {j}. {segment.get('speaker', 'N/A')} | "
                                     f"{segment.get('start_time', 0.0):.1f}s-{segment.get('end_time', 0.0):.1f}s | "
                                     f"融合情感: {segment.get('fused_emotion', 'N/A')}")
                        
                        if len(segments) > 3:
                            print(f"  ... 还有 {len(segments) - 3} 个片段")
                    
                    # 显示情感统计
                    if summary:
                        print(f"\n📈 情感统计:")
                        emotion_stats = summary.get('emotion_statistics', {})
                        for emotion, count in emotion_stats.items():
                            print(f"  {emotion}: {count} 次")
                    
                    print(f"⏱️  处理时间: {multi_result.get('processing_time', {}).get('total_time', 0.0):.2f}秒")
                    
            except Exception as e:
                print(f"❌ 多说话人分析异常: {e}")
            
            # 如果有多个文件，询问是否继续
            if i < len(test_files):
                try:
                    continue_test = input(f"\n继续测试下一个文件? (y/n): ").strip().lower()
                    if continue_test not in ['y', 'yes', '']:
                        print("测试已停止")
                        break
                except KeyboardInterrupt:
                    print("\n\n👋 测试已取消")
                    break
        
        print("\n🎉 综合测试完成！")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 确保所有依赖都已正确安装")
        print("2. 检查Python路径设置")
        print("3. 运行: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_dependencies():
    """测试依赖安装状态"""
    print("🔧 检查系统依赖")
    print("="*40)
    
    dependencies = [
        ('torch', 'PyTorch'),
        ('numpy', 'NumPy'),
        ('librosa', 'Librosa'),
        ('soundfile', 'SoundFile'),
        ('transformers', 'Transformers'),
        ('jieba', 'Jieba'),
        ('funasr', 'FunASR'),
        ('pyannote.audio', 'PyAnnote Audio'),
        ('sklearn', 'Scikit-learn')
    ]
    
    missing_deps = []
    
    for module, name in dependencies:
        try:
            mod = __import__(module)
            version = getattr(mod, '__version__', '未知版本')
            print(f"✅ {name}: {version}")
        except ImportError:
            print(f"❌ {name}: 未安装")
            missing_deps.append(name)
    
    if missing_deps:
        print(f"\n⚠️  缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ 所有依赖都已安装")
        return True

if __name__ == "__main__":
    print("🧪 多模态对话分析系统测试套件")
    print("="*60)
    
    # 首先检查依赖
    if test_dependencies():
        print("\n")
        # 然后进行综合测试
        test_comprehensive_system()
    else:
        print("\n❌ 请先安装缺少的依赖，然后重新运行测试")