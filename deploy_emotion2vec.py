#!/usr/bin/env python3
"""
emotion2vec 本地部署示例
支持两种使用方式：
1. emotion2vec+ - 情感识别模型（推荐）
2. emotion2vec - 情感特征提取模型
"""

import os
import sys
import numpy as np
import soundfile as sf
from pathlib import Path

def install_dependencies():
    """安装必要的依赖"""
    print("正在安装依赖...")
    os.system("pip install -U funasr torch soundfile numpy")
    print("依赖安装完成！")

def test_emotion_recognition(audio_file=None):
    """
    使用emotion2vec+进行情感识别
    支持9类情感：angry, disgusted, fearful, happy, neutral, other, sad, surprised, unknown
    """
    try:
        from funasr import AutoModel
        
        print("正在加载emotion2vec+模型...")
        
        # 使用最新的large模型
        model_id = "iic/emotion2vec_plus_large"
        
        model = AutoModel(
            model=model_id,
            hub="ms",  # 中国大陆用户使用"ms"，海外用户使用"hf"
        )
        
        print("模型加载完成！")
        
        # 如果没有指定音频文件，使用测试文件
        if audio_file is None:
            wav_file = f"{model.model_path}/example/test.wav"
        else:
            wav_file = audio_file
            
        print(f"正在分析音频文件: {wav_file}")
        
        # 进行情感识别
        rec_result = model.generate(
            wav_file, 
            output_dir="./outputs", 
            granularity="utterance", 
            extract_embedding=False
        )
        
        # 解析结果
        emotions = ['angry', 'disgusted', 'fearful', 'happy', 'neutral', 'other', 'sad', 'surprised', 'unknown']
        
        print("\n=== 情感识别结果 ===")
        if 'labels' in rec_result[0] and 'scores' in rec_result[0]:
            labels = rec_result[0]['labels']
            scores = rec_result[0]['scores']
            
            print(f"预测情感: {emotions[labels[0]]}")
            print(f"置信度: {scores[0]:.4f}")
            
            print("\n所有情感的得分:")
            for i, (emotion, score) in enumerate(zip(emotions, scores)):
                print(f"  {emotion}: {score:.4f}")
        else:
            print("结果:", rec_result)
            
        return rec_result
        
    except Exception as e:
        print(f"情感识别出错: {e}")
        print("请确保已正确安装funasr: pip install -U funasr")
        return None

def test_feature_extraction(audio_file=None):
    """
    使用emotion2vec进行特征提取
    """
    try:
        from funasr import AutoModel
        
        print("正在加载emotion2vec特征提取模型...")
        
        model_id = "iic/emotion2vec_base"
        
        model = AutoModel(
            model=model_id,
            hub="ms",
        )
        
        print("模型加载完成！")
        
        # 如果没有指定音频文件，使用测试文件
        if audio_file is None:
            wav_file = f"{model.model_path}/example/test.wav"
        else:
            wav_file = audio_file
            
        print(f"正在提取特征: {wav_file}")
        
        # 提取特征
        rec_result = model.generate(
            wav_file, 
            output_dir="./outputs", 
            granularity="utterance"
        )
        
        print("\n=== 特征提取结果 ===")
        if 'feats' in rec_result[0]:
            feats = rec_result[0]['feats']
            print(f"特征维度: {len(feats)}")
            print(f"特征前10维: {feats[:10]}")
            
            # 保存特征
            output_file = "extracted_features.npy"
            np.save(output_file, feats)
            print(f"特征已保存到: {output_file}")
        else:
            print("结果:", rec_result)
            
        return rec_result
        
    except Exception as e:
        print(f"特征提取出错: {e}")
        return None

def process_audio_file(file_path):
    """处理用户指定的音频文件"""
    if not os.path.exists(file_path):
        print(f"音频文件不存在: {file_path}")
        return
        
    # 检查音频格式
    try:
        info = sf.info(file_path)
        print(f"音频信息: 采样率={info.samplerate}Hz, 声道数={info.channels}, 时长={info.duration:.2f}秒")
        
        if info.samplerate != 16000:
            print("警告: emotion2vec需要16kHz采样率的音频，当前音频可能需要重采样")
        if info.channels != 1:
            print("警告: emotion2vec需要单声道音频，当前音频可能需要转换")
            
    except Exception as e:
        print(f"无法读取音频文件: {e}")
        return
    
    print("\n选择功能:")
    print("1. 情感识别 (emotion2vec+)")
    print("2. 特征提取 (emotion2vec)")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        test_emotion_recognition(file_path)
    elif choice == "2":
        test_feature_extraction(file_path)
    else:
        print("无效选择")

def main():
    print("=== emotion2vec 本地部署工具 ===")
    print()
    
    # 检查是否安装了funasr
    try:
        import funasr
        print("✓ funasr已安装")
    except ImportError:
        print("× funasr未安装，正在安装...")
        install_dependencies()
    
    # 检查是否有"惊讶.wav"文件
    surprise_file = "惊讶.wav"
    if os.path.exists(surprise_file):
        print(f"✓ 发现测试文件: {surprise_file}")
    
    print("\n选择使用方式:")
    print("1. 使用测试音频进行情感识别")
    print("2. 使用测试音频进行特征提取")
    print("3. 处理自定义音频文件")
    if os.path.exists(surprise_file):
        print("4. 分析'惊讶.wav'文件")
        print("5. 安装/更新依赖")
    else:
        print("4. 安装/更新依赖")
    
    choice = input("\n请输入选择: ").strip()
    
    if choice == "1":
        print("\n=== 情感识别测试 ===")
        test_emotion_recognition()
    elif choice == "2":
        print("\n=== 特征提取测试 ===")
        test_feature_extraction()
    elif choice == "3":
        audio_path = input("请输入音频文件路径: ").strip()
        process_audio_file(audio_path)
    elif choice == "4" and os.path.exists(surprise_file):
        print(f"\n=== 分析 {surprise_file} ===")
        process_audio_file(surprise_file)
    elif choice == "4" and not os.path.exists(surprise_file):
        install_dependencies()
    elif choice == "5" and os.path.exists(surprise_file):
        install_dependencies()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()