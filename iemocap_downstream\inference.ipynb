{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ang\n"]}], "source": ["import torch\n", "from model import BaseModel\n", "\n", "label_dict={'ang': 0, 'hap': 1, 'neu': 2, 'sad': 3}\n", "idx2label = {v: k for k, v in label_dict.items()}\n", "model = BaseModel(input_dim=768, output_dim=len(label_dict))\n", "\n", "ckpt = torch.load('outputs/2024-01-14/22-57-42/model_1.pth')\n", "model.load_state_dict(ckpt)\n", "\n", "feat = torch.randn(1, 100, 768)\n", "padding_mask = torch.zeros(1, 100).bool()\n", "outputs = model(feat, padding_mask)\n", "\n", "_, predict = torch.max(outputs.data, dim=1)\n", "print(idx2label[predict.item()])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 2}