"""
多模态对话分析系统配置文件
"""

import os

class Config:
    """系统配置类"""
    
    # 基础路径配置
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    OUTPUT_DIR = os.path.join(BASE_DIR, "outputs")
    
    # 音频处理配置
    AUDIO_CONFIG = {
        "sample_rate": 16000,
        "channels": 1,
        "supported_formats": [".wav", ".mp3", ".flac", ".m4a"],
        "max_duration": 300,  # 最大音频长度（秒）
    }
    
    # ASR配置
    ASR_CONFIG = {
        "model_name": "iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "device": "auto",  # auto, cpu, cuda
        "batch_size": 1,
        "confidence_threshold": 0.5,
    }
    
    # 音频情感分析配置
    AUDIO_EMOTION_CONFIG = {
        "model_name": "iic/emotion2vec_plus_base",
        "device": "auto",
        "confidence_threshold": 0.6,
        "emotion_labels": [
            "生气/angry", "厌恶/disgusted", "恐惧/fearful", 
            "开心/happy", "中立/neutral", "难过/sad", "吃惊/surprised"
        ]
    }
    
    # 文本处理配置
    TEXT_CONFIG = {
        "max_length": 512,
        "min_length": 1,
        "remove_punctuation": False,
        "use_jieba": True,
    }
    
    # 文本情感分析配置
    TEXT_EMOTION_CONFIG = {
        "method": "rule_based",  # rule_based, bert_based
        "bert_model": "hfl/chinese-roberta-wwm-ext",
        "confidence_threshold": 0.5,
        "emotion_keywords": {
            "开心": ["开心", "高兴", "快乐", "愉快", "兴奋", "满意", "喜悦"],
            "难过": ["难过", "伤心", "悲伤", "沮丧", "失望", "痛苦", "忧郁"],
            "生气": ["生气", "愤怒", "恼火", "气愤", "暴躁", "不满", "愤慨"],
            "恐惧": ["害怕", "恐惧", "担心", "紧张", "焦虑", "不安", "惊慌"],
            "吃惊": ["惊讶", "吃惊", "震惊", "意外", "惊奇", "诧异", "惊愕"],
            "厌恶": ["厌恶", "讨厌", "恶心", "反感", "嫌弃", "排斥", "憎恶"],
            "中立": ["还好", "一般", "普通", "正常", "平常", "无所谓", "没感觉"]
        }
    }
    
    # 多模态融合配置
    FUSION_CONFIG = {
        "method": "weighted_average",  # weighted_average, attention, gated
        "audio_weight": 0.6,
        "text_weight": 0.4,
        "consistency_threshold": 0.8,
        "confidence_boost": 0.1,  # 一致性时的置信度提升
        "confidence_penalty": 0.2,  # 不一致时的置信度惩罚
    }
    
    # 输出配置
    OUTPUT_CONFIG = {
        "save_results": True,
        "save_format": "json",  # json, txt, csv
        "include_raw_data": False,
        "timestamp_format": "%Y-%m-%d_%H-%M-%S",
        "report_language": "zh",  # zh, en
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "save_logs": True,
        "log_file": "multimodal_analysis.log"
    }
    
    # 性能配置
    PERFORMANCE_CONFIG = {
        "enable_gpu": True,
        "max_memory_usage": "4GB",
        "enable_caching": True,
        "cache_size": 100,
        "parallel_processing": False,
    }
    
    @classmethod
    def get_emotion_mapping(cls):
        """获取情感标签映射"""
        return {
            "生气/angry": "生气",
            "厌恶/disgusted": "厌恶", 
            "恐惧/fearful": "恐惧",
            "开心/happy": "开心",
            "中立/neutral": "中立",
            "难过/sad": "难过",
            "吃惊/surprised": "吃惊"
        }
    
    @classmethod
    def create_output_dir(cls):
        """创建输出目录"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        return cls.OUTPUT_DIR
    
    @classmethod
    def get_device(cls):
        """获取推荐的设备"""
        import torch
        if torch.cuda.is_available() and cls.PERFORMANCE_CONFIG["enable_gpu"]:
            return "cuda"
        else:
            return "cpu"